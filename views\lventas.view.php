<?php
#region DOCS

/** @var array $ventas Lista de ventas (inicialmente vacía, se llena via AJAX) */
/** @var array $clientes Lista de clientes activos para filtro */
/** @var array $centros_costos Lista de centros de costo activos para filtro */
/** @var string|null $success_text Success message to display */
/** @var string|null $success_display Whether to show success message ('show' or null) */
/** @var string|null $error_text Error message to display */
/** @var string|null $error_display Whether to show error message ('show' or null) */

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Consulta de Ventas</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>

	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/general/head.view.php'; ?>
	<?php #endregion HEAD ?>

	<!-- Include Bootstrap Datepicker CSS -->
	<link href="<?php echo RUTA_ADM_ASSETS; ?>plugins/bootstrap-datepicker/dist/css/bootstrap-datepicker.css" rel="stylesheet"/>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/general/header.view.php'; ?>

	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/general/sidebar.view.php'; ?>

	<!-- BEGIN #content -->
	<div id="content" class="app-content">

            <?php #region PAGE HEADER ?>
            <div class="d-flex align-items-center mb-3">
                <div>
                    <h4 class="mb-0">Consulta de Ventas</h4>
                    <p class="mb-0 text-muted">Gestión y consulta de ventas realizadas</p>
                </div>
                <div class="ms-auto">
                    <a href="crear-venta" class="btn btn-success">
                        <i class="fa fa-plus-circle fa-fw me-1"></i> Crear Nueva Venta
                    </a>
                </div>
            </div>

            <hr>
            <?php #endregion PAGE HEADER ?>

            <!-- BEGIN filters-panel -->
            <div class="panel panel-inverse">
                <div class="panel-heading">
                    <h4 class="panel-title">Filtros de Búsqueda</h4>
                </div>
                <div class="panel-body">
                    <form id="filtros-form" class="row g-3">
                        <!-- ID Venta Filter -->
                        <div class="col-md-2">
                            <label for="filtro_id_venta" class="form-label">Num. de venta</label>
                            <input type="number" class="form-control" id="filtro_id_venta" name="filtro_id_venta"
                                   placeholder="Número de venta" min="1">
                        </div>

                        <!-- Cliente Filter -->
                        <div class="col-md-3">
                            <label for="filtro_cliente" class="form-label">Cliente</label>
                            <select class="form-select" id="filtro_cliente" name="filtro_cliente">
                                <option value="">Todos los clientes</option>
                                <?php foreach ($clientes as $cliente): ?>
                                    <option value="<?php echo $cliente->getId(); ?>">
                                        <?php echo htmlspecialchars($cliente->getNombre()); ?>
                                        <?php if ($cliente->getCelular()): ?>
                                            - <?php echo htmlspecialchars($cliente->getCelular()); ?>
                                        <?php endif; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <!-- Date Range Filters -->
                        <div class="col-md-2">
                            <label for="fecha_desde" class="form-label">Fecha Desde</label>
                            <div class="input-group">
                                <input type="text" class="form-control datepicker" id="fecha_desde" name="fecha_desde"
                                       placeholder="yyyy-mm-dd">
                                <span class="input-group-text" id="fecha_desde_icon" style="cursor: pointer;">
                                    <i class="fa fa-calendar"></i>
                                </span>
                            </div>
                        </div>

                        <div class="col-md-2">
                            <label for="fecha_hasta" class="form-label">Fecha Hasta</label>
                            <div class="input-group">
                                <input type="text" class="form-control datepicker" id="fecha_hasta" name="fecha_hasta"
                                       placeholder="yyyy-mm-dd">
                                <span class="input-group-text" id="fecha_hasta_icon" style="cursor: pointer;">
                                    <i class="fa fa-calendar"></i>
                                </span>
                            </div>
                        </div>

                        <!-- Centro Costo Filter -->
                        <div class="col-md-3">
                            <label for="filtro_centro_costo" class="form-label">Centro de Costo</label>
                            <select class="form-select" id="filtro_centro_costo" name="filtro_centro_costo">
                                <option value="">Todos los centros</option>
                                <?php foreach ($centros_costos as $centro): ?>
                                    <option value="<?php echo $centro->getId(); ?>">
                                        <?php echo htmlspecialchars($centro->getNombre()); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <!-- Search Button -->
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fa fa-search"></i> Buscar
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            <!-- END filters-panel -->

            <!-- BEGIN results-panel -->
            <div id="results-panel" style="display: none;">
                <div class="panel panel-inverse">
                    <div class="panel-heading">
                        <h4 class="panel-title">Resultados de Búsqueda</h4>
                        <div class="panel-heading-btn">
                            <button type="button" class="btn btn-info btn-sm" id="btn-ver-agrupado" style="display: none;" onclick="verVentasAgrupadasPorMetodoPago()">
                                <i class="fa fa-chart-pie me-1"></i> Ver agrupado por métodos de pago
                            </button>
                        </div>
                    </div>
                    <div class="panel-body p-0">
                        <!-- Loading overlay -->
                        <div id="loading-overlay" style="display: none; position: relative; min-height: 200px;">
                            <div class="d-flex justify-content-center align-items-center h-100">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Cargando...</span>
                                </div>
                            </div>
                        </div>

                        <!-- Results table -->
                        <div id="results-table-container" style="overflow-x: auto;">
                            <table class="table table-hover table-sm mb-0" id="ventas-table">
                                <thead class="table-dark">
                                    <tr>
                                        <th class="text-center" style="width: 120px;">Acciones</th>
                                        <th style="width: 60px;">#</th>
                                        <th style="width: 120px;">Fecha</th>
                                        <th>Cliente</th>
                                        <th>Centro de Costo</th>
                                        <th>Método de Pago</th>
                                        <th class="text-end" style="width: 120px;">Valor Total</th>
                                    </tr>
                                </thead>
                                <tbody id="ventas-table-body">
                                    <!-- Results will be populated via AJAX -->
                                </tbody>
                                <tfoot id="ventas-table-footer" style="display: none;">
                                    <tr class="table-info">
                                        <th colspan="6" class="text-end fw-bold">Total Ventas:</th>
                                        <th class="text-end fw-bold" id="ventas-total-valor">$0</th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>

                        <!-- No results message -->
                        <div id="no-results" style="display: none;" class="text-center p-4">
                            <i class="fa fa-search fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No se encontraron ventas</h5>
                            <p class="text-muted">Intente ajustar los filtros de búsqueda</p>
                        </div>
                    </div>
                </div>
            </div>
            <!-- END results-panel -->
    </div>
    <!-- END #content -->
</div>
<!-- END #app -->

<!-- Modal for Venta Details -->
<div class="modal fade" id="ventaDetallesModal" tabindex="-1" aria-labelledby="ventaDetallesModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="ventaDetallesModalLabel">Detalles de la Venta</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Loading indicator -->
                <div id="venta-modal-loading" class="text-center py-4">
                    <i class="fa fa-spinner fa-spin fa-3x text-primary mb-3"></i>
                    <h5 class="text-primary">Cargando detalles...</h5>
                </div>

                <!-- Venta details content -->
                <div id="venta-modal-content" style="display: none;">
                    <!-- Venta Header Information -->
                    <div class="panel panel-inverse mb-3">
                        <div class="panel-heading">
                            <h4 class="panel-title">Información General</h4>
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-primary">Número de Venta:</label>
                                        <div id="detail-numero-venta" class="form-control-plaintext text-white fs-5"></div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-primary">Fecha:</label>
                                        <div id="detail-fecha-venta" class="form-control-plaintext text-white fs-5"></div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-primary">Cliente:</label>
                                        <div id="detail-cliente-venta" class="form-control-plaintext text-white fs-5"></div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-primary">Centro de Costo:</label>
                                        <div id="detail-centro-costo-venta" class="form-control-plaintext text-white fs-5"></div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-primary">Método de Pago:</label>
                                        <div id="detail-metodo-pago-venta" class="form-control-plaintext text-white fs-5"></div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-primary">Usuario:</label>
                                        <div id="detail-usuario-venta" class="form-control-plaintext text-white fs-5"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Venta Details Table -->
                    <div class="panel panel-inverse">
                        <div class="panel-heading">
                            <h4 class="panel-title">Detalle de Productos</h4>
                        </div>
                        <div class="panel-body">
                            <div class="table-responsive">
                                <table class="table table-hover table-sm mb-0" id="venta-details-table">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>Producto</th>
                                            <th class="text-center" style="width: 100px;">Cantidad</th>
                                            <th class="text-end" style="width: 120px;">Valor Unitario</th>
                                            <th class="text-end" style="width: 120px;">Valor Total</th>
                                        </tr>
                                    </thead>
                                    <tbody id="venta-details-table-body">
                                        <!-- Details will be populated via AJAX -->
                                    </tbody>
                                    <tfoot>
                                        <tr class="table-info">
                                            <th colspan="3" class="text-end fw-bold">Total General:</th>
                                            <th class="text-end fw-bold" id="detail-total-general-venta"></th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="venta-detalles-error" style="display: none;" class="alert alert-danger">
                    <i class="fa fa-exclamation-triangle"></i>
                    <span id="venta-detalles-error-message">Error al cargar los detalles</span>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal for Ventas Agrupadas por Método de Pago -->
<div class="modal fade" id="ventasAgrupadasModal" tabindex="-1" aria-labelledby="ventasAgrupadasModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="ventasAgrupadasModalLabel">Ventas Agrupadas por Método de Pago</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Loading indicator -->
                <div id="agrupadas-modal-loading" class="text-center py-4">
                    <i class="fa fa-spinner fa-spin fa-3x text-primary mb-3"></i>
                    <h5 class="text-primary">Cargando datos agrupados...</h5>
                </div>

                <!-- Agrupadas content -->
                <div id="agrupadas-modal-content" style="display: none;">
                    <!-- Agrupadas Table -->
                    <div class="panel panel-inverse">
                        <div class="panel-heading">
                            <h4 class="panel-title">Resumen por Método de Pago</h4>
                        </div>
                        <div>
                            <div class="table-responsive">
                                <table class="table table-hover table-sm mb-0" id="agrupadas-table">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>Método de Pago</th>
                                            <th class="text-end" style="width: 150px;">Total Ventas</th>
                                        </tr>
                                    </thead>
                                    <tbody id="agrupadas-table-body">
                                        <!-- Data will be populated via AJAX -->
                                    </tbody>
                                    <tfoot>
                                        <tr style="background-color: #D9E2F3; color: #000;">
                                            <th class="text-end fw-bold" style="color: #000;">Totales:</th>
                                            <th class="text-end fw-bold" id="agrupadas-total-general" style="color: #000;"></th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="agrupadas-error" style="display: none;" class="alert alert-danger">
                    <i class="fa fa-exclamation-triangle"></i>
                    <span id="agrupadas-error-message">Error al cargar los datos agrupados</span>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
            </div>
        </div>
    </div>
</div>

<!-- ================== BEGIN core-js ================== -->
<?php require_once __ROOT__ . '/views/general/core_js.view.php'; ?>
<!-- ================== END core-js ================== -->

<!-- Include Bootstrap Datepicker JS -->
<script src="<?php echo RUTA_ADM_ASSETS; ?>plugins/bootstrap-datepicker/dist/js/bootstrap-datepicker.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize datepickers
    $('.datepicker').datepicker({
        autoclose: true,
        todayHighlight: true,
        format: 'yyyy-mm-dd'
    });

    // Make calendar icons clickable
    document.getElementById('fecha_desde_icon').addEventListener('click', function() {
        $('#fecha_desde').datepicker('show');
    });

    document.getElementById('fecha_hasta_icon').addEventListener('click', function() {
        $('#fecha_hasta').datepicker('show');
    });

    // Elements
    const filtrosForm           = document.getElementById('filtros-form');
    const resultsPanel          = document.getElementById('results-panel');
    const loadingOverlay        = document.getElementById('loading-overlay');
    const resultsTableContainer = document.getElementById('results-table-container');
    const ventasTableBody       = document.getElementById('ventas-table-body');
    const noResults             = document.getElementById('no-results');
    const ventasTableFooter     = document.getElementById('ventas-table-footer'); // Added

    // Handle form submission
    filtrosForm.addEventListener('submit', function(event) {
        event.preventDefault();
        buscarVentas();
    });

    // Search function
    function buscarVentas() {
        const formData = new FormData(filtrosForm);

        // Show loading
        resultsPanel.style.display          = 'block';
        loadingOverlay.style.display        = 'block';
        resultsTableContainer.style.display = 'none';
        noResults.style.display             = 'none';

        // Add action to form data
        formData.append('action', 'search_ventas');

        // Get filter values for display
        const clienteSelect = document.getElementById('filtro_cliente');
        const clienteText   = clienteSelect.options[clienteSelect.selectedIndex].text;
        const fechaDesde    = document.getElementById('fecha_desde').value;
        const fechaHasta    = document.getElementById('fecha_hasta').value;
        const centroSelect  = document.getElementById('filtro_centro_costo');
        const centroText    = centroSelect.options[centroSelect.selectedIndex].text;
        const idVenta       = document.getElementById('filtro_id_venta').value;

        // Build search term for cliente
        let terminoCliente = '';
        if (clienteSelect.value) {
            // If specific client selected, use the text for search
            terminoCliente = clienteText.split(' - ')[0]; // Get name part
        }

        // Update form data with search terms
        formData.set('termino_cliente', terminoCliente);
        formData.set('fecha_desde', fechaDesde);
        formData.set('fecha_hasta', fechaHasta);
        formData.set('id_centro_costo', centroSelect.value);
        formData.set('id_venta', idVenta);

        // Submit search
        fetch('ventas', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            loadingOverlay.style.display = 'none';

            if (data.success) {
                if (data.ventas && data.ventas.length > 0) {
                    displayResults(data.ventas);
                } else {
                    showNoResults();
                }
            } else {
                showError(data.message || 'Error al buscar ventas');
            }
        })
        .catch(error => {
            loadingOverlay.style.display = 'none';
            showError('Error de conexión al buscar ventas');
            if (resultsCount) {
                resultsCount.textContent = 'Error en la búsqueda';
            }
            console.error('Error:', error);
        });
    }

    // Display results in table
    function displayResults(ventas) {
        ventasTableBody.innerHTML = '';
        let totalGeneralVentas = 0;

        ventas.forEach(venta => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td class="text-center">
                    <button class="btn btn-info btn-xs me-1" onclick="verDetallesVenta(${venta.id})" title="Ver detalles">
                        <i class="fa fa-eye"></i>
                    </button>
                    <button class="btn btn-danger btn-xs" onclick="eliminarVenta(${venta.id})" title="Eliminar venta">
                        <i class="fa fa-trash"></i>
                    </button>
                </td>
                <td>${venta.id}</td>
                <td>${venta.fecha_formateada}</td>
                <td>${venta.cliente_nombre}</td>
                <td>${venta.centro_costo_nombre}</td>
                <td>${venta.metodo_pago_nombre}</td>
                <td class="text-end">${venta.valor_total_formateado}</td>
            `;
            ventasTableBody.appendChild(row);
            totalGeneralVentas += parseFloat(venta.valor_total) || 0;
        });

        resultsTableContainer.style.display = 'block';
        noResults.style.display = 'none';

        // Show and update the total
        const ventasTotalValor = document.getElementById('ventas-total-valor');
        const btnVerAgrupado = document.getElementById('btn-ver-agrupado');

        if (ventas.length > 0) {
            const totalFormateado = '$' + new Intl.NumberFormat('es-CO').format(totalGeneralVentas);
            ventasTotalValor.textContent = totalFormateado;
            ventasTableFooter.style.display = 'table-footer-group';
            // Show the grouped view button when there are results
            btnVerAgrupado.style.display = 'inline-block';
        } else {
            ventasTableFooter.style.display = 'none';
            // Hide the grouped view button when there are no results
            btnVerAgrupado.style.display = 'none';
        }
    }

    // Show no results message
    function showNoResults() {
        resultsTableContainer.style.display = 'none';
        noResults.style.display = 'block';
        // Hide table footer when no results
        ventasTableFooter.style.display = 'none';
        // Hide the grouped view button when there are no results
        document.getElementById('btn-ver-agrupado').style.display = 'none';
    }

    // Show error message using SweetAlert
    function showError(message) {
        if (typeof showSweetAlertError === 'function') {
            showSweetAlertError('Error', message);
        } else {
            // Fallback to basic alert if SweetAlert is not available
            alert('Error: ' + message);
        }
    }

    // Global function for delete button
    window.eliminarVenta = function(ventaId) {
        swal({
            title: "¿Está seguro?",
            text: "Esta acción eliminará la venta y restaurará el inventario. No se puede deshacer.",
            icon: "warning",
            buttons: {
                cancel: {
                    text: "Cancelar",
                    value: null,
                    visible: true,
                    className: "btn-secondary",
                    closeModal: true,
                },
                confirm: {
                    text: "Sí, eliminar",
                    value: true,
                    visible: true,
                    className: "btn-danger",
                    closeModal: true
                }
            }
        }).then((willDelete) => {
            if (willDelete) {
                const formData = new FormData();
                formData.append('action', 'delete_venta');
                formData.append('venta_id', ventaId);

                fetch('ventas', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showSweetAlertSuccess('Eliminado', data.message);
                        // Refresh results
                        buscarVentas();
                    } else {
                        showSweetAlertError('Error', data.message);
                    }
                })
                .catch(error => {
                    showSweetAlertError('Error', 'Error de conexión al eliminar venta');
                    console.error('Error:', error);
                });
            }
        });
    };

    // Global function for details button
    window.verDetallesVenta = function(ventaId) {
        const modal = new bootstrap.Modal(document.getElementById('ventaDetallesModal'));
        const modalLoading = document.getElementById('venta-modal-loading');
        const modalContent = document.getElementById('venta-modal-content');

        // Show modal and loading state
        modal.show();
        modalLoading.style.display = 'block';
        modalContent.style.display = 'none';
        document.getElementById('venta-detalles-error').style.display = 'none';

        // Fetch venta details
        const formData = new FormData();
        formData.append('action', 'get_venta_details');
        formData.append('venta_id', ventaId);

        fetch('ventas', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            modalLoading.style.display = 'none';

            if (data.success) {
                populateVentaDetails(data.venta, data.detalles);
                modalContent.style.display = 'block';
            } else {
                document.getElementById('venta-detalles-error-message').textContent = data.message || 'Error al cargar los detalles de la venta';
                document.getElementById('venta-detalles-error').style.display = 'block';
            }
        })
        .catch(error => {
            modalLoading.style.display = 'none';
            document.getElementById('venta-detalles-error-message').textContent = 'Error de conexión al cargar los detalles de la venta';
            document.getElementById('venta-detalles-error').style.display = 'block';
            console.error('Error:', error);
        });
    };

    // Function to populate venta details in modal
    function populateVentaDetails(venta, detalles) {
        // Populate header information
        document.getElementById('detail-numero-venta').textContent = venta.id;
        document.getElementById('detail-fecha-venta').textContent = venta.fecha_formateada;
        document.getElementById('detail-cliente-venta').textContent = venta.cliente_nombre || 'Cliente no registrado';
        document.getElementById('detail-centro-costo-venta').textContent = venta.centro_costo_nombre;
        document.getElementById('detail-metodo-pago-venta').textContent = venta.metodo_pago_nombre;
        document.getElementById('detail-usuario-venta').textContent = venta.usuario_nombre || 'N/A';
        document.getElementById('detail-total-general-venta').textContent = venta.valor_total_formateado;

        // Populate details table
        const detailsTableBody = document.getElementById('venta-details-table-body');
        detailsTableBody.innerHTML = '';

        if (detalles && detalles.length > 0) {
            detalles.forEach(detalle => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="align-middle">${detalle.producto_descripcion}</td>
                    <td class="text-center align-middle">${detalle.cantidad}</td>
                    <td class="text-end align-middle">${detalle.valor_formateado}</td>
                    <td class="text-end align-middle">${detalle.valor_total_formateado}</td>
                `;
                detailsTableBody.appendChild(row);
            });
        } else {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td colspan="4" class="text-center text-muted">No se encontraron detalles para esta venta</td>
            `;
            detailsTableBody.appendChild(row);
        }
    }

    // Global function for viewing grouped sales by payment method
    window.verVentasAgrupadasPorMetodoPago = function() {
        const modal = new bootstrap.Modal(document.getElementById('ventasAgrupadasModal'));
        const modalLoading = document.getElementById('agrupadas-modal-loading');
        const modalContent = document.getElementById('agrupadas-modal-content');
        const modalError = document.getElementById('agrupadas-error');

        // Show modal and loading state
        modal.show();
        modalLoading.style.display = 'block';
        modalContent.style.display = 'none';
        modalError.style.display = 'none';

        // Get current filter values (same as main search)
        const formData = new FormData(filtrosForm);

        // Get filter values for display
        const clienteSelect = document.getElementById('filtro_cliente');
        const clienteText   = clienteSelect.options[clienteSelect.selectedIndex].text;
        const fechaDesde    = document.getElementById('fecha_desde').value;
        const fechaHasta    = document.getElementById('fecha_hasta').value;
        const centroSelect  = document.getElementById('filtro_centro_costo');
        const idVenta       = document.getElementById('filtro_id_venta').value;

        // Build search term for cliente
        let terminoCliente = '';
        if (clienteSelect.value) {
            // If specific client selected, use the text for search
            terminoCliente = clienteText.split(' - ')[0]; // Get name part
        }

        // Prepare form data with same filters as main search
        formData.set('action', 'get_ventas_agrupadas_metodo_pago');
        formData.set('termino_cliente', terminoCliente);
        formData.set('fecha_desde', fechaDesde);
        formData.set('fecha_hasta', fechaHasta);
        formData.set('id_centro_costo', centroSelect.value);
        formData.set('id_venta', idVenta);

        // Fetch grouped data
        fetch('ventas', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            modalLoading.style.display = 'none';

            if (data.success) {
                populateVentasAgrupadas(data.datos, data.total_general_formateado);
                modalContent.style.display = 'block';
            } else {
                document.getElementById('agrupadas-error-message').textContent = data.message || 'Error al cargar los datos agrupados';
                modalError.style.display = 'block';
            }
        })
        .catch(error => {
            modalLoading.style.display = 'none';
            document.getElementById('agrupadas-error-message').textContent = 'Error de conexión al cargar los datos agrupados';
            modalError.style.display = 'block';
            console.error('Error:', error);
        });
    };

    // Function to populate grouped sales data in modal
    function populateVentasAgrupadas(datos, totalGeneralFormateado) {
        const agrupadasTableBody = document.getElementById('agrupadas-table-body');
        agrupadasTableBody.innerHTML = '';

        if (datos && datos.length > 0) {
            datos.forEach(dato => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="align-middle">${dato.metodo_pago_nombre}</td>
                    <td class="text-end align-middle">${dato.total_ventas_formateado}</td>
                `;
                agrupadasTableBody.appendChild(row);
            });
        } else {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td colspan="2" class="text-center text-muted">No se encontraron datos para mostrar</td>
            `;
            agrupadasTableBody.appendChild(row);
        }

        // Update total
        document.getElementById('agrupadas-total-general').textContent = totalGeneralFormateado;
    }

    // Clear modal content when modal is hidden
    document.getElementById('ventasAgrupadasModal').addEventListener('hidden.bs.modal', function () {
        document.getElementById('agrupadas-table-body').innerHTML = '';
        document.getElementById('agrupadas-total-general').textContent = '';
        document.getElementById('agrupadas-modal-content').style.display = 'none';
        document.getElementById('agrupadas-error').style.display = 'none';
    });
});
</script>
</body>
</html>
