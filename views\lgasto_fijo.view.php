<?php
/**
 * Vista para la gestión de gastos fijos
 *
 * Variables disponibles:
 * @var array $gastos_fijos Lista de gastos fijos activos
 * @var string $error_display Estado de visualización de errores ('show' o 'hide')
 * @var string $error_text Texto del mensaje de error
 * @var string $success_display Estado de visualización de éxito ('show' o 'hide')
 * @var string $success_text Texto del mensaje de éxito
 */
?>

<!DOCTYPE html>
<html lang="es" class="dark-mode">
<head>
    <meta charset="utf-8"/>
    <title><?php echo APP_NAME; ?> | Gestión de Gastos Fijos</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>

    <?php #region HEAD ?>
    <?php require_once __ROOT__ . '/views/general/head.view.php'; ?>
    <?php #endregion HEAD ?>

    <!-- Toast Notifications CSS -->
    <link href="resources/css/toast-notifications.css" rel="stylesheet" />
</head>

<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
    <!-- #header -->
    <?php require_once __ROOT__ . '/views/general/header.view.php'; ?>

    <!-- #sidebar -->
    <?php require_once __ROOT__ . '/views/general/sidebar.view.php'; ?>

    <!-- BEGIN #content -->
    <div id="content" class="app-content">

                    <?php #region PAGE HEADER ?>
                    <div class="d-flex align-items-center mb-3">
                        <div>
                            <h4 class="mb-0">Gestión de Gastos Fijos</h4>
                            <p class="mb-0 text-muted">Administración de gastos fijos del sistema</p>
                        </div>
                        <div class="ms-auto">
                            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#createGastoFijoModal">
                                <i class="fa fa-plus-circle fa-fw me-1"></i> Crear Nuevo Gasto Fijo
                            </button>
                        </div>
                    </div>

                    <hr>
                    <?php #endregion PAGE HEADER ?>

                    <?php #region SEARCH FILTERS ?>
                    <!-- Search Filters Panel -->
                    <div class="panel panel-inverse">
                        <div class="panel-heading">
                            <h4 class="panel-title">Filtros de Búsqueda</h4>
                        </div>
                        <div class="panel-body">
                            <form id="search-form">
                                <div class="row g-3">
                                    <!-- Description Filter -->
                                    <div class="col-md-8">
                                        <label for="termino_descripcion" class="form-label">Descripción</label>
                                        <input type="text" class="form-control" id="termino_descripcion" name="termino_descripcion"
                                            placeholder="Buscar por descripción...">
                                    </div>

                                    <!-- Search Button -->
                                    <div class="col-md-4 d-flex align-items-end">
                                        <button type="submit" class="btn btn-primary me-2">
                                            <i class="fa fa-search fa-fw me-1"></i> Buscar
                                        </button>
                                        <button type="button" class="btn btn-secondary" id="btn-limpiar">
                                            <i class="fa fa-eraser fa-fw me-1"></i> Limpiar
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    <?php #endregion SEARCH FILTERS ?>

                    <?php #region RESULTS ?>
                    <!-- Results Panel -->
                    <div class="panel panel-inverse">
                        <div class="panel-heading">
                            <h4 class="panel-title">Resultados de la Búsqueda</h4>
                            <div class="panel-heading-btn">
                                <a href="balances-gastos-fijos" class="btn btn-info btn-sm">
                                    <i class="fa fa-balance-scale me-1"></i>
                                    Balance de Gastos Fijos
                                </a>
                            </div>
                        </div>
                        <div>
                            <!-- No results message -->
                            <div id="no-results" class="text-center py-4" style="display: none;">
                                <i class="fa fa-search fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">Utilice los filtros para buscar gastos fijos</h5>
                                <p class="text-muted">Los resultados aparecerán aquí una vez que realice una búsqueda.</p>
                            </div>

                            <!-- Loading indicator -->
                            <div id="loading" class="text-center py-4" style="display: none;">
                                <i class="fa fa-spinner fa-spin fa-3x text-primary mb-3"></i>
                                <h5 class="text-primary">Buscando gastos fijos...</h5>
                            </div>

                            <!-- Results table -->
                            <div id="results-table-container">
                                <table class="table table-hover table-sm mb-0" id="gastos-fijos-table">
                                    <thead class="table-dark">
                                        <tr>
                                            <th class="text-center" style="width: 120px;">Acciones</th>
                                            <th>Descripción</th>
                                            <th class="text-end" style="width: 150px;">Valor</th>
                                        </tr>
                                    </thead>
                                    <tbody id="gastos-fijos-table-body">
                                        <?php foreach ($gastos_fijos as $gasto_fijo): ?>
                                            <tr>
                                                <td class="text-center align-middle">
                                                    <button class="btn btn-warning btn-xs me-1" onclick="editarGastoFijo(<?= $gasto_fijo->getId() ?>, '<?= htmlspecialchars($gasto_fijo->getDescripcion()) ?>', <?= $gasto_fijo->getValor() ?>)" title="Editar gasto fijo">
                                                        <i class="fa fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-danger btn-xs" onclick="eliminarGastoFijo(<?= $gasto_fijo->getId() ?>, '<?= htmlspecialchars($gasto_fijo->getDescripcion()) ?>')" title="Eliminar gasto fijo">
                                                        <i class="fa fa-trash"></i>
                                                    </button>
                                                </td>
                                                <td class="align-middle"><?= htmlspecialchars($gasto_fijo->getDescripcion()) ?></td>
                                                <td class="text-end align-middle">$<?= number_format($gasto_fijo->getValor(), 0, ',', '.') ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <?php #endregion RESULTS ?>

    </div>
    <!-- END #content -->

    <?php #region Create Gasto Fijo Modal ?>
    <div class="modal fade" id="createGastoFijoModal" tabindex="-1" aria-labelledby="createGastoFijoModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <form id="create-gasto-fijo-form">
                    <div class="modal-header">
                        <h5 class="modal-title" id="createGastoFijoModalLabel">Crear Nuevo Gasto Fijo</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="create-descripcion" class="form-label">Descripción: <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="create-descripcion" name="descripcion" required>
                        </div>

                        <div class="mb-3">
                            <label for="create-valor" class="form-label">Valor: <span class="text-danger">*</span></label>
                            <input type="text" class="form-control currency-input" id="create-valor" name="valor"
                                   data-type="currency" placeholder="$0" required>
                        </div>

                        <div class="alert alert-danger" id="create-gasto-fijo-error" style="display: none;"></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-success">Crear Gasto Fijo</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <?php #endregion Create Gasto Fijo Modal ?>

    <?php #region Edit Gasto Fijo Modal ?>
    <div class="modal fade" id="editGastoFijoModal" tabindex="-1" aria-labelledby="editGastoFijoModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <form id="edit-gasto-fijo-form">
                    <div class="modal-header">
                        <h5 class="modal-title" id="editGastoFijoModalLabel">Editar Gasto Fijo</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <input type="hidden" id="edit-id" name="id">

                        <!-- Information Section -->
                        <div class="panel panel-inverse mb-3">
                            <div class="panel-heading">
                                <h4 class="panel-title">Información General</h4>
                            </div>
                            <div class="panel-body">
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="mb-3">
                                            <label class="form-label fw-bold text-primary">Descripción:</label>
                                            <div id="detail-descripcion" class="form-control-plaintext text-white fs-5"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Edit Form -->
                        <div class="mb-3">
                            <label for="edit-valor" class="form-label">Valor: <span class="text-danger">*</span></label>
                            <input type="text" class="form-control currency-input" id="edit-valor" name="valor"
                                   data-type="currency" placeholder="$0" required>
                        </div>

                        <div class="alert alert-danger" id="edit-gasto-fijo-error" style="display: none;"></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-warning">Actualizar Gasto Fijo</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <?php #endregion Edit Gasto Fijo Modal ?>

    <!-- BEGIN scroll-top-btn -->
    <a href="javascript:;" class="btn btn-icon btn-circle btn-theme btn-scroll-to-top" data-toggle="scroll-to-top">
        <i class="fa fa-angle-up"></i>
    </a>
    <!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<!-- Toast Container -->
<div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 1055;">
    <div id="toast-notification" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <i id="toast-icon" class="fa fa-check-circle text-success me-2"></i>
            <strong id="toast-title" class="me-auto">Notificación</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body" id="toast-message">
            Mensaje de notificación
        </div>
    </div>
</div>

<?php #region JS ?>
<?php require_once __ROOT__ . '/views/general/core_js.view.php'; ?>

<!-- Include formatcurrency.js for currency formatting -->
<script src="<?php echo RUTA_RESOURCES; ?>js/formatcurrency.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Modal Elements
    const createGastoFijoModal = new bootstrap.Modal(document.getElementById('createGastoFijoModal'));
    const editGastoFijoModal   = new bootstrap.Modal(document.getElementById('editGastoFijoModal'));

    // Form Elements
    const createGastoFijoForm   = document.getElementById('create-gasto-fijo-form');
    const editGastoFijoForm     = document.getElementById('edit-gasto-fijo-form');
    const searchForm            = document.getElementById('search-form');
    const btnLimpiar            = document.getElementById('btn-limpiar');
    const loading               = document.getElementById('loading');
    const resultsTableContainer = document.getElementById('results-table-container');
    const gastosFijosTableBody  = document.getElementById('gastos-fijos-table-body');

    // Initialize currency formatting for modals
    $('#createGastoFijoModal').on('shown.bs.modal', function () {
        $('input[data-type="currency"]').each(function() {
            $(this).on({
                keyup: function() { formatCurrency($(this)); },
                blur: function() { formatCurrency($(this), "blur"); }
            });
        });
    });

    $('#editGastoFijoModal').on('shown.bs.modal', function () {
        $('input[data-type="currency"]').each(function() {
            $(this).on({
                keyup: function() { formatCurrency($(this)); },
                blur: function() { formatCurrency($(this), "blur"); }
            });
        });
    });

    // Search form submission
    searchForm.addEventListener('submit', function(e) {
        e.preventDefault();
        searchGastosFijos();
    });

    // Clear button
    btnLimpiar.addEventListener('click', function() {
        searchForm.reset();
        // Reload initial data
        location.reload();
    });

    // Create Gasto Fijo Form
    createGastoFijoForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(createGastoFijoForm);
        formData.append('action', 'create_gasto_fijo');

        fetch('gastos-fijos', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                createGastoFijoModal.hide();
                createGastoFijoForm.reset();
                document.getElementById('create-gasto-fijo-error').style.display = 'none';

                // Show success message
                showToastNotification('success', 'Éxito', data.message);

                // Reload the page to show updated data
                setTimeout(() => location.reload(), 1000);
            } else {
                document.getElementById('create-gasto-fijo-error').textContent = data.message;
                document.getElementById('create-gasto-fijo-error').style.display = 'block';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('create-gasto-fijo-error').textContent = 'Error de conexión al crear el gasto fijo.';
            document.getElementById('create-gasto-fijo-error').style.display = 'block';
        });
    });

    // Edit Gasto Fijo Form
    editGastoFijoForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(editGastoFijoForm);
        formData.append('action', 'edit_gasto_fijo');

        fetch('gastos-fijos', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                editGastoFijoModal.hide();
                editGastoFijoForm.reset();
                document.getElementById('edit-gasto-fijo-error').style.display = 'none';

                // Show success message
                showToastNotification('success', 'Éxito', data.message);

                // Reload the page to show updated data
                setTimeout(() => location.reload(), 1000);
            } else {
                document.getElementById('edit-gasto-fijo-error').textContent = data.message;
                document.getElementById('edit-gasto-fijo-error').style.display = 'block';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('edit-gasto-fijo-error').textContent = 'Error de conexión al actualizar el gasto fijo.';
            document.getElementById('edit-gasto-fijo-error').style.display = 'block';
        });
    });

    // Search function
    function searchGastosFijos() {
        const formData = new FormData(searchForm);
        formData.append('action', 'search_gastos_fijos');

        // Show loading
        document.getElementById('no-results').style.display = 'none';
        loading.style.display = 'block';
        resultsTableContainer.style.display = 'none';

        fetch('gastos-fijos', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            loading.style.display = 'none';

            if (data.success) {
                displayResults(data.gastos_fijos);
            } else {
                swal({
                    title: "Error",
                    text: data.message || 'Error al buscar gastos fijos',
                    icon: "error",
                    button: {
                        text: "Cerrar",
                        value: null,
                        visible: true,
                        className: "btn-danger",
                        closeModal: true
                    }
                });
                resultsTableContainer.style.display = 'block';
            }
        })
        .catch(error => {
            loading.style.display = 'none';
            swal({
                title: "Error de Conexión",
                text: 'Error de conexión al buscar gastos fijos',
                icon: "error",
                button: {
                    text: "Cerrar",
                    value: null,
                    visible: true,
                    className: "btn-danger",
                    closeModal: true
                }
            });
            resultsTableContainer.style.display = 'block';
            console.error('Error:', error);
        });
    }

    // Display results in table
    function displayResults(gastosFijos) {
        gastosFijosTableBody.innerHTML = '';

        gastosFijos.forEach(gastoFijo => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td class="text-center align-middle">
                    <button class="btn btn-warning btn-xs me-1" onclick="editarGastoFijo(${gastoFijo.id}, '${gastoFijo.descripcion.replace(/'/g, "\\'")}', ${gastoFijo.valor})" title="Editar gasto fijo">
                        <i class="fa fa-edit"></i>
                    </button>
                    <button class="btn btn-danger btn-xs" onclick="eliminarGastoFijo(${gastoFijo.id}, '${gastoFijo.descripcion.replace(/'/g, "\\'")}')" title="Eliminar gasto fijo">
                        <i class="fa fa-trash"></i>
                    </button>
                </td>
                <td class="align-middle">${gastoFijo.descripcion}</td>
                <td class="text-end align-middle">${gastoFijo.valor_formateado}</td>
            `;
            gastosFijosTableBody.appendChild(row);
        });

        resultsTableContainer.style.display = 'block';
    }

    // Toast notification function
    function showToastNotification(type, title, message) {
        const toast = document.getElementById('toast-notification');
        const toastIcon = document.getElementById('toast-icon');
        const toastTitle = document.getElementById('toast-title');
        const toastMessage = document.getElementById('toast-message');

        // Configure toast based on type
        if (type === 'success') {
            toastIcon.className = 'fa fa-check-circle text-success me-2';
            toast.className = 'toast border-success';
        } else if (type === 'error') {
            toastIcon.className = 'fa fa-exclamation-circle text-danger me-2';
            toast.className = 'toast border-danger';
        } else if (type === 'warning') {
            toastIcon.className = 'fa fa-exclamation-triangle text-warning me-2';
            toast.className = 'toast border-warning';
        } else {
            toastIcon.className = 'fa fa-info-circle text-info me-2';
            toast.className = 'toast border-info';
        }

        toastTitle.textContent = title;
        toastMessage.textContent = message;

        // Show toast
        const bsToast = new bootstrap.Toast(toast, {
            autohide: true,
            delay: 4000
        });
        bsToast.show();
    }
});

// Global functions for button actions
window.editarGastoFijo = function(id, descripcion, valor) {
    const modal = new bootstrap.Modal(document.getElementById('editGastoFijoModal'));

    // Populate information section
    document.getElementById('detail-descripcion').textContent = descripcion;

    // Populate form
    document.getElementById('edit-id').value = id;
    document.getElementById('edit-valor').value = '$' + Math.round(valor).toLocaleString('es-CO').replace(/,/g, '.');

    // Hide error message
    document.getElementById('edit-gasto-fijo-error').style.display = 'none';

    modal.show();
};

window.eliminarGastoFijo = function(id, descripcion) {
    swal({
        title: "¿Está seguro?",
        text: `¿Desea eliminar el gasto fijo "${descripcion}"? Esta acción no se puede deshacer.`,
        icon: "warning",
        buttons: {
            cancel: {
                text      : "Cancelar",
                value     : null,
                visible   : true,
                className : "btn-secondary",
                closeModal: true,
            },
            confirm: {
                text      : "Sí, eliminar",
                value     : true,
                visible   : true,
                className : "btn-danger",
                closeModal: true
            }
        }
    }).then((willDelete) => {
        if (willDelete) {
            deleteGastoFijo(id);
        }
    });
};

function deleteGastoFijo(id) {
    const formData = new FormData();
    formData.append('action', 'delete_gasto_fijo');
    formData.append('id', id);

    fetch('gastos-fijos', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show toast notification
            const toast = document.getElementById('toast-notification');
            const toastIcon = document.getElementById('toast-icon');
            const toastTitle = document.getElementById('toast-title');
            const toastMessage = document.getElementById('toast-message');

            toastIcon.className = 'fa fa-check-circle text-success me-2';
            toast.className = 'toast border-success';
            toastTitle.textContent = 'Éxito';
            toastMessage.textContent = data.message;

            const bsToast = new bootstrap.Toast(toast, {
                autohide: true,
                delay: 4000
            });
            bsToast.show();

            // Reload page after short delay
            setTimeout(() => location.reload(), 1000);
        } else {
            swal({
                title : "Error",
                text  : data.message,
                icon  : "error",
                button: {
                    text      : "Cerrar",
                    value     : null,
                    visible   : true,
                    className : "btn-danger",
                    closeModal: true
                }
            });
        }
    })
    .catch(error => {
        console.error('Error:', error);
        swal({
            title : "Error de Conexión",
            text  : 'Error de conexión al eliminar el gasto fijo',
            icon  : "error",
            button: {
                text      : "Cerrar",
                value     : null,
                visible   : true,
                className : "btn-danger",
                closeModal: true
            }
        });
    });
}
</script>
<?php #endregion JS ?>

