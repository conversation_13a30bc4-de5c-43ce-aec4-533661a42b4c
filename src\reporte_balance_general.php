<?php

declare(strict_types=1);

use App\classes\Cita;
use App\classes\Venta;
use App\classes\OrdenCompra;
use App\classes\GastoOperativo;
use App\classes\CentroCosto;
use App\classes\GastoFijoBalance;

// PHPSpreadsheet imports for Excel export
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
    error_log("Error crítico: No hay conexión a la base de datos en reporte_balance_general.php.");
    $response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
    http_response_code(503); // Service Unavailable
    echo json_encode($response);
    exit;
}

// Variables para manejo de errores y mensajes
$error_display   = null;
$error_text      = null;
$success_display = null;
$success_text    = null;

// Variables para los datos del reporte
$reporte_data        = null;
$mes_seleccionado    = '';
$anio_seleccionado   = '';
$id_centro_costo     = null;
$centro_costo_nombre = '';

#region Handle POST Actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    #region Generate Report
    if ($action === 'generar_reporte') {
        $mes_seleccionado  = trim($_POST['mes_seleccionado'] ?? '');
        $anio_seleccionado = trim($_POST['anio_seleccionado'] ?? '');

        // Validaciones
        if (empty($mes_seleccionado) || empty($anio_seleccionado)) {
            $error_display = 'show';
            $error_text    = 'Debe seleccionar un mes y año.';
        } elseif (!is_numeric($mes_seleccionado) || $mes_seleccionado < 1 || $mes_seleccionado > 12) {
            $error_display = 'show';
            $error_text    = 'El mes seleccionado no es válido.';
        } elseif (!is_numeric($anio_seleccionado) || $anio_seleccionado < 2020 || $anio_seleccionado > 2030) {
            $error_display = 'show';
            $error_text    = 'El año seleccionado no es válido.';
        } else {
            try {
                // Establecer zona horaria
                date_default_timezone_set('America/Bogota');

                // Obtener centro de costo de la sesión
                $id_centro_costo = $_SESSION[CENTRO_COSTO_SESSION] ?? null;
                if (!$id_centro_costo) {
                    $error_display = 'show';
                    $error_text    = 'No hay un centro de costo seleccionado en la sesión.';
                } else {
                    // Obtener nombre del centro de costo
                    $centro_costo = CentroCosto::get($id_centro_costo, $conexion);
                    if ($centro_costo) {
                        $centro_costo_nombre = $centro_costo->getNombre();
                    }

                    // Generar datos del reporte
                    $reporte_data = generarReporteBalanceGeneral($conexion, (int)$mes_seleccionado, (int)$anio_seleccionado, $id_centro_costo);

                    if (empty($reporte_data['dias_balance'])) {
                        $error_display = 'show';
                        $error_text    = 'No se encontraron datos para el mes y año seleccionados.';
                    }
                }

            } catch (Exception $e) {
                $error_display = 'show';
                $error_text    = 'Error al generar el reporte: ' . $e->getMessage();
            }
        }
    }
    #endregion Generate Report

    #region Get Balance Details
    if ($action === 'get_balance_details') {
        header('Content-Type: application/json');

        try {
            $fecha = trim($_POST['fecha'] ?? '');

            // Validaciones
            if (empty($fecha)) {
                echo json_encode(['success' => false, 'message' => 'Fecha requerida.']);
                exit;
            }

            // Obtener centro de costo de la sesión
            $id_centro_costo = $_SESSION[CENTRO_COSTO_SESSION] ?? null;
            if (!$id_centro_costo) {
                echo json_encode(['success' => false, 'message' => 'No hay un centro de costo seleccionado en la sesión.']);
                exit;
            }

            // Obtener registros detallados
            $citas = Cita::obtenerRegistrosPorFecha($fecha, $id_centro_costo, $conexion);
            $ventas = Venta::obtenerRegistrosPorFecha($fecha, $id_centro_costo, $conexion);
            $ordenes = OrdenCompra::obtenerRegistrosPorFecha($fecha, $id_centro_costo, $conexion);
            $gastos = GastoOperativo::obtenerRegistrosPorFecha($fecha, $id_centro_costo, $conexion);

            // Calcular totales
            $total_ingresos_citas = array_sum(array_column($citas, 'valor_total'));
            $total_ingresos_ventas = array_sum(array_column($ventas, 'valor_total'));
            $total_egresos_ordenes = array_sum(array_column($ordenes, 'valor_total'));
            $total_egresos_gastos = array_sum(array_column($gastos, 'valor'));

            $total_ingresos = $total_ingresos_citas + $total_ingresos_ventas;
            $total_egresos = $total_egresos_ordenes + $total_egresos_gastos;

            echo json_encode([
                'success' => true,
                'data' => [
                    'fecha' => $fecha,
                    'citas' => $citas,
                    'ventas' => $ventas,
                    'ordenes' => $ordenes,
                    'gastos' => $gastos,
                    'totales' => [
                        'ingresos_citas' => $total_ingresos_citas,
                        'ingresos_ventas' => $total_ingresos_ventas,
                        'total_ingresos' => $total_ingresos,
                        'egresos_ordenes' => $total_egresos_ordenes,
                        'egresos_gastos' => $total_egresos_gastos,
                        'total_egresos' => $total_egresos,
                        'ingresos_citas_formateado' => '$' . number_format($total_ingresos_citas, 0, ',', '.'),
                        'ingresos_ventas_formateado' => '$' . number_format($total_ingresos_ventas, 0, ',', '.'),
                        'total_ingresos_formateado' => '$' . number_format($total_ingresos, 0, ',', '.'),
                        'egresos_ordenes_formateado' => '$' . number_format($total_egresos_ordenes, 0, ',', '.'),
                        'egresos_gastos_formateado' => '$' . number_format($total_egresos_gastos, 0, ',', '.'),
                        'total_egresos_formateado' => '$' . number_format($total_egresos, 0, ',', '.')
                    ]
                ]
            ]);
            exit;

        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => 'Error al obtener los detalles: ' . $e->getMessage()]);
            exit;
        }
    }
    #endregion Get Balance Details

    #region Get Gastos Fijos
    if ($action === 'get_gastos_fijos') {
        header('Content-Type: application/json');

        try {
            $mes = trim($_POST['mes'] ?? '');
            $anio = trim($_POST['anio'] ?? '');

            // Validaciones
            if (empty($mes) || empty($anio)) {
                echo json_encode(['success' => false, 'message' => 'Mes y año requeridos.']);
                exit;
            }

            if (!is_numeric($mes) || !is_numeric($anio)) {
                echo json_encode(['success' => false, 'message' => 'Mes y año deben ser números válidos.']);
                exit;
            }

            $mes = (int)$mes;
            $anio = (int)$anio;

            if ($mes < 1 || $mes > 12) {
                echo json_encode(['success' => false, 'message' => 'El mes debe estar entre 1 y 12.']);
                exit;
            }

            // Obtener centro de costo de la sesión
            $id_centro_costo = $_SESSION[CENTRO_COSTO_SESSION] ?? null;
            if (!$id_centro_costo) {
                echo json_encode(['success' => false, 'message' => 'No hay un centro de costo seleccionado en la sesión.']);
                exit;
            }

            // Obtener gastos fijos para el mes/año/centro de costo
            $gastos_fijos = GastoFijoBalance::obtenerPorMesAnio($mes, $anio, $conexion, $id_centro_costo);

            // Formatear datos para la respuesta
            $gastos_formateados = [];
            foreach ($gastos_fijos as $gasto) {
                $gastos_formateados[] = [
                    'id' => $gasto->getId(),
                    'id_gasto_fijo' => $gasto->getId_gasto_fijo(),
                    'id_centro_costo' => $gasto->getId_centro_costo(),
                    'valor' => $gasto->getValor(),
                    'mes' => $gasto->getMes(),
                    'anio' => $gasto->getAnio(),
                    'gasto_fijo_descripcion' => $gasto->getGasto_fijo_descripcion(),
                    'centro_costo_nombre' => $gasto->getCentro_costo_nombre(),
                    'valor_formateado' => $gasto->getValorFormateado()
                ];
            }

            echo json_encode([
                'success' => true,
                'data' => $gastos_formateados
            ]);
            exit;

        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => 'Error al obtener gastos fijos: ' . $e->getMessage()]);
            exit;
        }
    }
    #endregion Get Gastos Fijos

    #region Get Nómina Comisiones
    if ($action === 'get_nomina_comisiones') {
        header('Content-Type: application/json');

        try {
            $mes = trim($_POST['mes'] ?? '');
            $anio = trim($_POST['anio'] ?? '');

            // Validaciones
            if (empty($mes) || empty($anio)) {
                echo json_encode(['success' => false, 'message' => 'Mes y año requeridos.']);
                exit;
            }

            if (!is_numeric($mes) || !is_numeric($anio)) {
                echo json_encode(['success' => false, 'message' => 'Mes y año deben ser números válidos.']);
                exit;
            }

            $mes = (int)$mes;
            $anio = (int)$anio;

            if ($mes < 1 || $mes > 12) {
                echo json_encode(['success' => false, 'message' => 'El mes debe estar entre 1 y 12.']);
                exit;
            }

            // Obtener centro de costo de la sesión
            $id_centro_costo = $_SESSION[CENTRO_COSTO_SESSION] ?? null;
            if (!$id_centro_costo) {
                echo json_encode(['success' => false, 'message' => 'No hay un centro de costo seleccionado en la sesión.']);
                exit;
            }

            // Obtener comisiones agrupadas por empleado para el mes/año/centro de costo
            $comisiones = obtenerComisionesPorEmpleado($conexion, $mes, $anio, $id_centro_costo);

            echo json_encode([
                'success' => true,
                'data' => $comisiones
            ]);
            exit;

        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => 'Error al obtener comisiones: ' . $e->getMessage()]);
            exit;
        }
    }
    #endregion Get Nómina Comisiones

    #region Export Excel
    if ($action === 'exportar_excel') {
        try {
            $mes_seleccionado  = trim($_POST['mes_seleccionado'] ?? '');
            $anio_seleccionado = trim($_POST['anio_seleccionado'] ?? '');

            // Validaciones
            if (empty($mes_seleccionado) || empty($anio_seleccionado)) {
                $error_display = 'show';
                $error_text    = 'Debe seleccionar un mes y año para exportar.';
            } else {
                // Obtener centro de costo de la sesión
                $id_centro_costo = $_SESSION[CENTRO_COSTO_SESSION] ?? null;
                if (!$id_centro_costo) {
                    $error_display = 'show';
                    $error_text    = 'No hay un centro de costo seleccionado en la sesión.';
                } else {
                    // Obtener nombre del centro de costo
                    $centro_costo = CentroCosto::get($id_centro_costo, $conexion);
                    if ($centro_costo) {
                        $centro_costo_nombre = $centro_costo->getNombre();
                    }

                    // Generar datos del reporte
                    $reporte_data = generarReporteBalanceGeneral($conexion, (int)$mes_seleccionado, (int)$anio_seleccionado, $id_centro_costo);

                    if (empty($reporte_data['dias_balance'])) {
                        $error_display = 'show';
                        $error_text    = 'No se encontraron datos para exportar en el mes y año seleccionados.';
                    } else {
                        // Limpiar cualquier salida previa antes de generar Excel
                        while (ob_get_level()) {
                            ob_end_clean();
                        }

                        // Generar y descargar el archivo Excel
                        generarExcelBalanceGeneral($reporte_data, (int)$mes_seleccionado, (int)$anio_seleccionado, $centro_costo_nombre, $conexion);
                        exit; // Terminar la ejecución después de la descarga
                    }
                }
            }

        } catch (Exception $e) {
            $error_display = 'show';
            $error_text    = 'Error al exportar el reporte: ' . $e->getMessage();
        }
    }
    #endregion Export Excel
}
#endregion Handle POST Actions

#region try
try {
    // Establecer zona horaria
    date_default_timezone_set('America/Bogota');

    // Set default month and year to current date if not coming from POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        $mes_seleccionado = date('n'); // Current month (1-12)
        $anio_seleccionado = date('Y'); // Current year
    }

    // Obtener centro de costo de la sesión
    $id_centro_costo = $_SESSION[CENTRO_COSTO_SESSION] ?? null;
    if ($id_centro_costo) {
        $centro_costo = CentroCosto::get($id_centro_costo, $conexion);
        if ($centro_costo) {
            $centro_costo_nombre = $centro_costo->getNombre();
        }
    }

} catch (PDOException $e) {
    $error_display = 'show';
    $error_text    = "Error de base de datos al cargar los datos iniciales.";
} catch (Exception $e) {
    $error_display = 'show';
    $error_text    = "Ocurrió un error inesperado: " . $e->getMessage();
}
#endregion try

/**
 * Función para generar los datos del reporte de balance general
 */
function generarReporteBalanceGeneral(PDO $conexion, int $mes, int $anio, int $id_centro_costo): array
{
    try {
        // Calcular el número de días en el mes
        $dias_en_mes = cal_days_in_month(CAL_GREGORIAN, $mes, $anio);
        
        $dias_balance = [];
        $total_ingresos = 0;
        $total_egresos = 0;
        
        // Iterar por cada día del mes
        for ($dia = 1; $dia <= $dias_en_mes; $dia++) {
            $fecha = sprintf('%04d-%02d-%02d', $anio, $mes, $dia);
            
            // Calcular ingresos del día (Citas + Ventas)
            $ingresos_citas     = Cita::calcularIngresosPorFecha($fecha, $id_centro_costo, $conexion);
            $ingresos_ventas    = Venta::calcularIngresosPorFecha($fecha, $id_centro_costo, $conexion);
            $total_ingresos_dia = $ingresos_citas + $ingresos_ventas;

            // Calcular egresos del día (Órdenes de Compra + Gastos Operativos)
            $egresos_ordenes   = OrdenCompra::calcularEgresosPorFecha($fecha, $id_centro_costo, $conexion);
            $egresos_gastos    = GastoOperativo::calcularEgresosPorFecha($fecha, $id_centro_costo, $conexion);
            $total_egresos_dia = $egresos_ordenes + $egresos_gastos;
            
            // Calcular balance del día
            $balance_dia = $total_ingresos_dia - $total_egresos_dia;
            
            $dias_balance[] = [
                'dia'                 => $dia,
                'fecha'               => $fecha,
                'ingresos'            => $total_ingresos_dia,
                'egresos'             => $total_egresos_dia,
                'balance'             => $balance_dia,
                'ingresos_formateado' => '$' . number_format($total_ingresos_dia, 0, ',', '.'),
                'egresos_formateado'  => '$' . number_format($total_egresos_dia, 0, ',', '.'),
                'balance_formateado'  => '$' . number_format($balance_dia, 0, ',', '.')
            ];
            
            $total_ingresos += $total_ingresos_dia;
            $total_egresos += $total_egresos_dia;
        }
        
        $balance_total = $total_ingresos - $total_egresos;

        // Obtener gastos operativos agrupados para todo el mes
        $gastos_agrupados_mes = obtenerGastosAgrupadosPorMes($conexion, $mes, $anio, $id_centro_costo);

        return [
            'dias_balance'              => $dias_balance,
            'total_ingresos'            => $total_ingresos,
            'total_egresos'             => $total_egresos,
            'balance_total'             => $balance_total,
            'total_ingresos_formateado' => '$' . number_format($total_ingresos, 0, ',', '.'),
            'total_egresos_formateado'  => '$' . number_format($total_egresos, 0, ',', '.'),
            'balance_total_formateado'  => '$' . number_format($balance_total, 0, ',', '.'),
            'mes_nombre'                => obtenerNombreMes($mes),
            'anio'                      => $anio,
            'gastos_agrupados'          => $gastos_agrupados_mes
        ];
        
    } catch (Exception $e) {
        throw new Exception("Error al generar el reporte de balance general: " . $e->getMessage());
    }
}

/**
 * Función auxiliar para obtener el nombre del mes
 */
function obtenerNombreMes(int $mes): string
{
    $meses = [
        1 => 'Enero',      2  => 'Febrero', 3  => 'Marzo',     4  => 'Abril',
        5 => 'Mayo',       6  => 'Junio',   7  => 'Julio',     8  => 'Agosto',
        9 => 'Septiembre', 10 => 'Octubre', 11 => 'Noviembre', 12 => 'Diciembre'
    ];

    return $meses[$mes] ?? 'Mes Desconocido';
}

/**
 * Función para obtener gastos operativos agrupados por descripción para un mes completo
 */
function obtenerGastosAgrupadosPorMes(PDO $conexion, int $mes, int $anio, int $id_centro_costo): array
{
    try {
        $query = <<<SQL
        SELECT
            go.descripcion,
            SUM(go.valor) as total
        FROM gastos_operativos go
        WHERE MONTH(go.fecha) = :mes
        AND YEAR(go.fecha) = :anio
        AND go.estado = 1
        AND go.id_cierre IS NOT NULL
        AND go.id_centro_costo = :id_centro_costo
        GROUP BY go.descripcion
        ORDER BY total DESC, go.descripcion ASC
        SQL;

        $statement = $conexion->prepare($query);
        $statement->bindValue(':mes', $mes, PDO::PARAM_INT);
        $statement->bindValue(':anio', $anio, PDO::PARAM_INT);
        $statement->bindValue(':id_centro_costo', $id_centro_costo, PDO::PARAM_INT);
        $statement->execute();
        $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

        // Format results
        $registros_formateados = [];
        foreach ($resultados as $resultado) {
            $registros_formateados[] = [
                'descripcion' => $resultado['descripcion'],
                'total' => (float)$resultado['total'],
                'total_formateado' => '$' . number_format((float)$resultado['total'], 0, ',', '.')
            ];
        }

        return $registros_formateados;

    } catch (PDOException $e) {
        throw new Exception("Error al obtener gastos agrupados por mes ($mes/$anio): " . $e->getMessage());
    }
}

/**
 * Función para obtener comisiones agrupadas por empleado para un mes completo
 */
function obtenerComisionesPorEmpleado(PDO $conexion, int $mes, int $anio, int $id_centro_costo): array
{
    try {
        $query = <<<SQL
        SELECT
            e.id as empleado_id,
            e.nombre as empleado_nombre,
            SUM(c.valor_comision_empleado) as total_comision
        FROM citas c
        INNER JOIN empleados_turnos et ON c.id_empleado_turno = et.id
        INNER JOIN empleados e ON et.id_empleado = e.id
        INNER JOIN puestos p ON et.id_puesto = p.id
        WHERE MONTH(c.fecha_fin) = :mes
        AND YEAR(c.fecha_fin) = :anio
        AND c.estado = 1
        AND c.fecha_fin IS NOT NULL
        AND c.id_cierre IS NOT NULL
        AND p.id_centro_costo = :id_centro_costo
        GROUP BY e.id, e.nombre
        HAVING total_comision > 0
        ORDER BY e.nombre ASC
        SQL;

        $statement = $conexion->prepare($query);
        $statement->bindValue(':mes', $mes, PDO::PARAM_INT);
        $statement->bindValue(':anio', $anio, PDO::PARAM_INT);
        $statement->bindValue(':id_centro_costo', $id_centro_costo, PDO::PARAM_INT);
        $statement->execute();
        $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

        // Format results
        $comisiones_formateadas = [];
        foreach ($resultados as $resultado) {
            $comisiones_formateadas[] = [
                'empleado_id' => (int)$resultado['empleado_id'],
                'empleado_nombre' => $resultado['empleado_nombre'],
                'total_comision' => (float)$resultado['total_comision'],
                'comision_formateada' => '$' . number_format((float)$resultado['total_comision'], 0, ',', '.')
            ];
        }

        return $comisiones_formateadas;

    } catch (PDOException $e) {
        throw new Exception("Error al obtener comisiones por empleado ($mes/$anio): " . $e->getMessage());
    }
}

/**
 * Función auxiliar para continuar la generación del Excel con las secciones adicionales
 */
function generarExcelBalanceGeneralContinuacion($sheet, int &$row, array $reporte_data, int $mes, int $anio, PDO $conexion, int $id_centro_costo, int $balanceHeaderRow): void
{
    // Start placing sections side-by-side in columns F onwards, aligned with balance header
    $currentRightRow = $balanceHeaderRow; // Start at exactly the same row as "BALANCE GENERAL" header

    // GASTOS OPERATIVOS SECTION (Column F-G)
    if (!empty($reporte_data['gastos_agrupados'])) {
        $gastosStartRow = $currentRightRow;
        $sheet->setCellValue('F' . $currentRightRow, 'GASTOS OPERATIVOS');
        $sheet->mergeCells('F' . $currentRightRow . ':G' . $currentRightRow);
        $sheet->getStyle('F' . $currentRightRow)->getFont()->setBold(true)->setSize(14);
        $sheet->getStyle('F' . $currentRightRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('F' . $currentRightRow . ':G' . $currentRightRow)->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('FFC000');
        $currentRightRow++;

        // Headers
        $sheet->setCellValue('F' . $currentRightRow, 'Descripción');
        $sheet->setCellValue('G' . $currentRightRow, 'Total');
        $sheet->getStyle('F' . $currentRightRow . ':G' . $currentRightRow)->getFont()->setBold(true);
        $sheet->getStyle('F' . $currentRightRow . ':G' . $currentRightRow)->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('FFE699');
        $currentRightRow++;

        // Data
        $totalGastosOperativos = 0;
        foreach ($reporte_data['gastos_agrupados'] as $gasto) {
            $sheet->setCellValue('F' . $currentRightRow, $gasto['descripcion']);
            $sheet->setCellValue('G' . $currentRightRow, $gasto['total']);
            $sheet->getStyle('G' . $currentRightRow)->getNumberFormat()->setFormatCode('#,##0');
            $sheet->getStyle('G' . $currentRightRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
            $totalGastosOperativos += $gasto['total'];
            $currentRightRow++;
        }

        // Total
        $sheet->setCellValue('F' . $currentRightRow, 'Totales:');
        $sheet->setCellValue('G' . $currentRightRow, $totalGastosOperativos);
        $sheet->getStyle('F' . $currentRightRow . ':G' . $currentRightRow)->getFont()->setBold(true);
        $sheet->getStyle('F' . $currentRightRow . ':G' . $currentRightRow)->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('D9E2F3');
        $sheet->getStyle('G' . $currentRightRow)->getNumberFormat()->setFormatCode('#,##0');
        $sheet->getStyle('G' . $currentRightRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

        // Apply borders
        $gastosEndRow = $currentRightRow;
        $sheet->getStyle('F' . $gastosStartRow . ':G' . $gastosEndRow)->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);
        $currentRightRow += 2;
    }

    // GASTOS FIJOS SECTION (Column F-G, below gastos operativos)
    $totalGastosFijos = 0;
    try {
        $gastos_fijos = GastoFijoBalance::obtenerPorMesAnio($mes, $anio, $conexion, $id_centro_costo);
        if (!empty($gastos_fijos)) {
            $fijoStartRow = $currentRightRow;
            $sheet->setCellValue('F' . $currentRightRow, 'GASTOS FIJOS');
            $sheet->mergeCells('F' . $currentRightRow . ':G' . $currentRightRow);
            $sheet->getStyle('F' . $currentRightRow)->getFont()->setBold(true)->setSize(14);
            $sheet->getStyle('F' . $currentRightRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
            $sheet->getStyle('F' . $currentRightRow . ':G' . $currentRightRow)->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('FF6B6B');
            $currentRightRow++;

            // Headers
            $sheet->setCellValue('F' . $currentRightRow, 'Descripción');
            $sheet->setCellValue('G' . $currentRightRow, 'Valor');
            $sheet->getStyle('F' . $currentRightRow . ':G' . $currentRightRow)->getFont()->setBold(true);
            $sheet->getStyle('F' . $currentRightRow . ':G' . $currentRightRow)->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('FFB3B3');
            $currentRightRow++;

            // Data
            foreach ($gastos_fijos as $gasto) {
                $sheet->setCellValue('F' . $currentRightRow, $gasto->getGasto_fijo_descripcion());
                $sheet->setCellValue('G' . $currentRightRow, $gasto->getValor());
                $sheet->getStyle('G' . $currentRightRow)->getNumberFormat()->setFormatCode('#,##0');
                $sheet->getStyle('G' . $currentRightRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
                $totalGastosFijos += $gasto->getValor();
                $currentRightRow++;
            }

            // Total
            $sheet->setCellValue('F' . $currentRightRow, 'Totales:');
            $sheet->setCellValue('G' . $currentRightRow, $totalGastosFijos);
            $sheet->getStyle('F' . $currentRightRow . ':G' . $currentRightRow)->getFont()->setBold(true);
            $sheet->getStyle('F' . $currentRightRow . ':G' . $currentRightRow)->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('D9E2F3');
            $sheet->getStyle('G' . $currentRightRow)->getNumberFormat()->setFormatCode('#,##0');
            $sheet->getStyle('G' . $currentRightRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

            // Apply borders
            $fijoEndRow = $currentRightRow;
            $sheet->getStyle('F' . $fijoStartRow . ':G' . $fijoEndRow)->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);
            $currentRightRow += 2;
        }
    } catch (Exception $e) {
        // Continue without gastos fijos if there's an error
    }

    // COMISIONES SECTION (Column F-G, below gastos fijos)
    $totalComisiones = 0;
    try {
        $comisiones = obtenerComisionesPorEmpleado($conexion, $mes, $anio, $id_centro_costo);
        if (!empty($comisiones)) {
            $comisionStartRow = $currentRightRow;
            $sheet->setCellValue('F' . $currentRightRow, 'COMISIONES DE EMPLEADOS');
            $sheet->mergeCells('F' . $currentRightRow . ':G' . $currentRightRow);
            $sheet->getStyle('F' . $currentRightRow)->getFont()->setBold(true)->setSize(14);
            $sheet->getStyle('F' . $currentRightRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
            $sheet->getStyle('F' . $currentRightRow . ':G' . $currentRightRow)->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('9966CC');
            $sheet->getStyle('F' . $currentRightRow . ':G' . $currentRightRow)->getFont()->getColor()->setRGB('FFFFFF');
            $currentRightRow++;

            // Headers
            $sheet->setCellValue('F' . $currentRightRow, 'Barbero');
            $sheet->setCellValue('G' . $currentRightRow, 'Comisión');
            $sheet->getStyle('F' . $currentRightRow . ':G' . $currentRightRow)->getFont()->setBold(true);
            $sheet->getStyle('F' . $currentRightRow . ':G' . $currentRightRow)->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('D9B3FF');
            $currentRightRow++;

            // Data
            foreach ($comisiones as $comision) {
                $sheet->setCellValue('F' . $currentRightRow, $comision['empleado_nombre']);
                $sheet->setCellValue('G' . $currentRightRow, $comision['total_comision']);
                $sheet->getStyle('G' . $currentRightRow)->getNumberFormat()->setFormatCode('#,##0');
                $sheet->getStyle('G' . $currentRightRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
                $totalComisiones += $comision['total_comision'];
                $currentRightRow++;
            }

            // Total
            $sheet->setCellValue('F' . $currentRightRow, 'Totales:');
            $sheet->setCellValue('G' . $currentRightRow, $totalComisiones);
            $sheet->getStyle('F' . $currentRightRow . ':G' . $currentRightRow)->getFont()->setBold(true);
            $sheet->getStyle('F' . $currentRightRow . ':G' . $currentRightRow)->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('D9E2F3');
            $sheet->getStyle('G' . $currentRightRow)->getNumberFormat()->setFormatCode('#,##0');
            $sheet->getStyle('G' . $currentRightRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

            // Apply borders
            $comisionEndRow = $currentRightRow;
            $sheet->getStyle('F' . $comisionStartRow . ':G' . $comisionEndRow)->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);
            $currentRightRow += 2;
        }
    } catch (Exception $e) {
        // Continue without comisiones if there's an error
    }

    // NET PROFIT SECTION - Place below the balance table, spanning A:D
    $totalEgresosCompletos = $reporte_data['total_egresos'] + $totalGastosFijos + $totalComisiones;
    $gananciaNeta = $reporte_data['total_ingresos'] - $totalEgresosCompletos;

    $netStartRow = $row + 1; // Add one row spacing after balance table
    $sheet->setCellValue('A' . $netStartRow, 'GANANCIA NETA');
    $sheet->mergeCells('A' . $netStartRow . ':D' . $netStartRow);
    $sheet->getStyle('A' . $netStartRow . ':D' . $netStartRow)->getFont()->setBold(true)->setSize(16);
    $sheet->getStyle('A' . $netStartRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
    $sheet->getStyle('A' . $netStartRow . ':D' . $netStartRow)->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('28A745');
    $sheet->getStyle('A' . $netStartRow . ':D' . $netStartRow)->getFont()->getColor()->setRGB('FFFFFF');
    $netStartRow++;

    // Breakdown
    $sheet->setCellValue('A' . $netStartRow, 'Total Ingresos:');
    $sheet->setCellValue('B' . $netStartRow, $reporte_data['total_ingresos']);
    $sheet->getStyle('A' . $netStartRow)->getFont()->setBold(true);
    $sheet->getStyle('B' . $netStartRow)->getNumberFormat()->setFormatCode('#,##0');
    $sheet->getStyle('B' . $netStartRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
    $netStartRow++;

    $sheet->setCellValue('A' . $netStartRow, 'Total Egresos:');
    $sheet->setCellValue('B' . $netStartRow, $totalEgresosCompletos);
    $sheet->getStyle('A' . $netStartRow)->getFont()->setBold(true);
    $sheet->getStyle('B' . $netStartRow)->getNumberFormat()->setFormatCode('#,##0');
    $sheet->getStyle('B' . $netStartRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
    $netStartRow++;

    $sheet->setCellValue('A' . $netStartRow, 'GANANCIA NETA:');
    $sheet->setCellValue('B' . $netStartRow, $gananciaNeta);
    $sheet->getStyle('A' . $netStartRow . ':D' . $netStartRow)->getFont()->setBold(true)->setSize(14);
    $sheet->getStyle('A' . $netStartRow . ':D' . $netStartRow)->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('C6EFCE');
    $sheet->getStyle('B' . $netStartRow)->getNumberFormat()->setFormatCode('#,##0');
    $sheet->getStyle('B' . $netStartRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

    // Apply borders to net profit section
    $netEndRow = $netStartRow;
    $netStartRowForBorders = $row + 1; // The actual start row for borders
    $sheet->getStyle('A' . $netStartRowForBorders . ':D' . $netEndRow)->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);

    // Adjust column widths
    $sheet->getColumnDimension('A')->setWidth(25);
    $sheet->getColumnDimension('B')->setWidth(15);
    $sheet->getColumnDimension('C')->setWidth(15);
    $sheet->getColumnDimension('D')->setWidth(15);
    $sheet->getColumnDimension('F')->setWidth(25);
    $sheet->getColumnDimension('G')->setWidth(15);
}

/**
 * Función para generar archivo Excel del reporte de balance general
 */
function generarExcelBalanceGeneral(array $reporte_data, int $mes, int $anio, string $centro_costo_nombre, PDO $conexion): void
{
    try {
        // Crear nuevo spreadsheet
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Configurar propiedades del documento
        $spreadsheet->getProperties()
            ->setCreator('Sistema de Gestión de Barbería')
            ->setTitle('Reporte de Balance General')
            ->setSubject('Reporte de Balance General')
            ->setDescription('Reporte detallado de balance general con ingresos, egresos y ganancia neta');

        // Configurar zona horaria
        date_default_timezone_set('America/Bogota');

        // Variables para el diseño
        $row = 1;

        // METADATA DEL REPORTE
        $sheet->setCellValue('A' . $row, 'REPORTE DE BALANCE GENERAL');
        $sheet->mergeCells('A' . $row . ':F' . $row);
        $sheet->getStyle('A' . $row)->getFont()->setBold(true)->setSize(16);
        $sheet->getStyle('A' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $row++;

        // Header section with borders
        $headerStartRow = $row;

        // Fecha de generación
        $sheet->setCellValue('A' . $row, 'Fecha de generación:');
        $sheet->setCellValue('B' . $row, date('Y-m-d H:i:s'));
        $sheet->mergeCells('B' . $row . ':D' . $row);
        $sheet->getStyle('A' . $row)->getFont()->setBold(true);
        $sheet->getStyle('B' . $row)->getFont()->setBold(true);
        $row++;

        // Centro de costo
        $sheet->setCellValue('A' . $row, 'Centro de costo:');
        $sheet->setCellValue('B' . $row, $centro_costo_nombre);
        $sheet->mergeCells('B' . $row . ':D' . $row);
        $sheet->getStyle('A' . $row)->getFont()->setBold(true);
        $sheet->getStyle('B' . $row)->getFont()->setBold(true);
        $row++;

        // Período
        $sheet->setCellValue('A' . $row, 'Período:');
        $sheet->setCellValue('B' . $row, obtenerNombreMes($mes) . ' ' . $anio);
        $sheet->mergeCells('B' . $row . ':D' . $row);
        $sheet->getStyle('A' . $row)->getFont()->setBold(true);
        $sheet->getStyle('B' . $row)->getFont()->setBold(true);

        // Apply borders to header section
        $headerEndRow = $row;
        $sheet->getStyle('A' . $headerStartRow . ':D' . $headerEndRow)->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);

        $row += 2; // Blank row for spacing

        // BALANCE GENERAL TABLE
        $balanceTableStartRow = $row;
        $balanceHeaderRow = $row; // Store the header row for right-side alignment
        $sheet->setCellValue('A' . $row, 'BALANCE GENERAL');
        $sheet->mergeCells('A' . $row . ':D' . $row);
        $sheet->getStyle('A' . $row . ':D' . $row)->getFont()->setBold(true)->setSize(14);
        $sheet->getStyle('A' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('A' . $row . ':D' . $row)->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('4472C4');
        $sheet->getStyle('A' . $row . ':D' . $row)->getFont()->getColor()->setRGB('FFFFFF');
        $row++;

        // Balance table headers
        $sheet->setCellValue('A' . $row, 'Día');
        $sheet->setCellValue('B' . $row, 'Ingresos');
        $sheet->setCellValue('C' . $row, 'Egresos');
        $sheet->setCellValue('D' . $row, 'Balance');
        $sheet->getStyle('A' . $row . ':D' . $row)->getFont()->setBold(true);
        $sheet->getStyle('A' . $row . ':D' . $row)->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('D9E2F3');
        $row++;

        // Balance table data - Include ALL days of the month
        foreach ($reporte_data['dias_balance'] as $dia_data) {
            $sheet->setCellValue('A' . $row, $dia_data['dia']);
            $sheet->setCellValue('B' . $row, $dia_data['ingresos']);
            $sheet->setCellValue('C' . $row, $dia_data['egresos']);
            $sheet->setCellValue('D' . $row, $dia_data['balance']);

            // Format currency
            $sheet->getStyle('B' . $row . ':D' . $row)->getNumberFormat()->setFormatCode('#,##0');
            $sheet->getStyle('B' . $row . ':D' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

            // Color balance based on positive/negative
            if ($dia_data['balance'] < 0) {
                $sheet->getStyle('D' . $row)->getFont()->getColor()->setRGB('FF0000');
            } else {
                $sheet->getStyle('D' . $row)->getFont()->getColor()->setRGB('008000');
            }

            $row++;
        }

        // Balance totals
        $sheet->setCellValue('A' . $row, 'Totales:');
        $sheet->setCellValue('B' . $row, $reporte_data['total_ingresos']);
        $sheet->setCellValue('C' . $row, $reporte_data['total_egresos']);
        $sheet->setCellValue('D' . $row, $reporte_data['balance_total']);
        $sheet->getStyle('A' . $row . ':D' . $row)->getFont()->setBold(true);
        $sheet->getStyle('A' . $row . ':D' . $row)->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('D9E2F3');
        $sheet->getStyle('B' . $row . ':D' . $row)->getNumberFormat()->setFormatCode('#,##0');
        $sheet->getStyle('B' . $row . ':D' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

        // Apply borders to balance table
        $balanceTableEndRow = $row;
        $sheet->getStyle('A' . $balanceTableStartRow . ':D' . $balanceTableEndRow)->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);

        $row += 2; // Blank row for spacing

        // Continue with the rest of the Excel generation in the next chunk...
        generarExcelBalanceGeneralContinuacion($sheet, $row, $reporte_data, $mes, $anio, $conexion, $_SESSION[CENTRO_COSTO_SESSION], $balanceHeaderRow);

        // Configure filename
        $mes_nombre = obtenerNombreMes($mes);
        $centro_costo_safe = preg_replace('/[^a-zA-Z0-9_-]/', '_', $centro_costo_nombre);
        $filename = "Balance_General_{$mes_nombre}_{$anio}_{$centro_costo_safe}.xlsx";

        // Configure headers for download
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');
        header('Cache-Control: max-age=1');
        header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');
        header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
        header('Cache-Control: cache, must-revalidate');
        header('Pragma: public');

        // Create writer and send file
        $writer = new Xlsx($spreadsheet);
        $writer->save('php://output');

    } catch (Exception $e) {
        throw new Exception("Error al generar el archivo Excel: " . $e->getMessage());
    }
}

// Include the view
require_once __ROOT__ . '/views/reporte_balance_general.view.php';

?>
