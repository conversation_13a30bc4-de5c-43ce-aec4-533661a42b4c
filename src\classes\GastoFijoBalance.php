<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;

class GastoFijoBalance
{
	// --- Atributos ---
	private ?int    $id                     = null;
	private ?int    $id_gasto_fijo          = null;
	private ?int    $id_centro_costo        = null;
	private ?float  $valor                  = null;
	private ?int    $mes                    = null;
	private ?int    $anio                   = null;
	private ?string $gasto_fijo_descripcion = null;  // Atributo para evitar consultas redundantes
	private ?string $centro_costo_nombre    = null;  // Atributo para evitar consultas redundantes

	/**
	 * Constructor: Inicializa las propiedades del objeto GastoFijoBalance.
	 *
	 * @param int   $id_gasto_fijo   ID del gasto fijo (obligatorio)
	 * @param int   $id_centro_costo ID del centro de costo (obligatorio)
	 * @param float $valor           Valor del balance (obligatorio)
	 * @param int   $mes             Mes del balance (obligatorio, 1-12)
	 * @param int   $anio            Año del balance (obligatorio)
	 */
	public function __construct(int $id_gasto_fijo, int $id_centro_costo, float $valor, int $mes, int $anio)
	{
		// Validar parámetros obligatorios
		if ($id_gasto_fijo <= 0) {
			throw new Exception("El ID del gasto fijo debe ser un número positivo.");
		}

		if ($id_centro_costo <= 0) {
			throw new Exception("El ID del centro de costo debe ser un número entero positivo.");
		}

		if (!$this->validarValorPositivo($valor)) {
			throw new Exception("El valor debe ser un número positivo mayor que cero.");
		}

		if (!$this->validarMes($mes)) {
			throw new Exception("El mes debe ser un número entre 1 y 12.");
		}

		if (!$this->validarAnio($anio)) {
			throw new Exception("El año debe ser un número válido (ej. 2023).");
		}

		$this->id                      = 0;
		$this->id_gasto_fijo          = $id_gasto_fijo;
		$this->id_centro_costo        = $id_centro_costo;
		$this->valor                  = $valor;
		$this->mes                    = $mes;
		$this->anio                   = $anio;
		$this->gasto_fijo_descripcion = null;
		$this->centro_costo_nombre    = null;
	}

	/**
	 * Método estático para construir un objeto GastoFijoBalance desde un array (ej. fila de DB).
	 *
	 * @param array $resultado Array asociativo con los datos del balance de gasto fijo.
	 *
	 * @return self Instancia de GastoFijoBalance.
	 * @throws Exception Si ocurre un error durante la construcción.
	 */
	public static function construct(array $resultado = []): self
	{
		try {
			// Validar que existan los campos requeridos
			if (!isset($resultado['id_gasto_fijo']) || !isset($resultado['id_centro_costo']) || !isset($resultado['valor']) || !isset($resultado['mes']) || !isset($resultado['anio'])) {
				throw new Exception("Faltan campos requeridos para construir GastoFijoBalance.");
			}

			$id_gasto_fijo = (int)$resultado['id_gasto_fijo'];
			$id_centro_costo = (int)$resultado['id_centro_costo'];
			$valor = (float)$resultado['valor'];
			$mes = (int)$resultado['mes'];
			$anio = (int)$resultado['anio'];

			$objeto = new self($id_gasto_fijo, $id_centro_costo, $valor, $mes, $anio);
			$objeto->id = isset($resultado['id']) ? (int)$resultado['id'] : 0;
			// $objeto->anio is set by the constructor
			$objeto->gasto_fijo_descripcion = $resultado['gasto_fijo_descripcion'] ?? null;
			$objeto->centro_costo_nombre = $resultado['centro_costo_nombre'] ?? null;

			return $objeto;
		} catch (Exception $e) {
			throw new Exception("Error al construir GastoFijoBalance: " . $e->getMessage());
		}
	}

	/**
	 * Obtiene un balance de gasto fijo por su ID.
	 *
	 * @param int $id       ID del balance de gasto fijo.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return self|null Objeto GastoFijoBalance o null si no se encuentra.
	 * @throws Exception Si hay error en DB.
	 */
	public static function obtenerPorId(int $id, PDO $conexion): ?self
	{
		try {
			// Consulta para obtener balance de gasto fijo por ID con información del gasto fijo
			$query = <<<SQL
            SELECT
            	gfb.*,
            	gf.descripcion AS gasto_fijo_descripcion
            FROM gastos_fijos_balance gfb
            LEFT JOIN gastos_fijos gf ON gfb.id_gasto_fijo = gf.id
            WHERE
            	gfb.id = :id
            LIMIT 1
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado ? self::construct($resultado) : null;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener GastoFijoBalance por ID: " . $e->getMessage());
		}
	}

	/**
	 * Obtiene una lista de balances de gastos fijos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 * @param int|null $mes Filtro opcional por mes.
	 * @param int|null $id_gasto_fijo Filtro opcional por gasto fijo.
	 * @param int|null $anio Filtro opcional por año.
	 * @param int|null $id_centro_costo Filtro opcional por centro de costo.
	 *
	 * @return array Array de objetos GastoFijoBalance.
	 * @throws Exception Si hay error en DB.
	 */
	public static function obtenerTodos(PDO $conexion, ?int $mes = null, ?int $id_gasto_fijo = null, ?int $anio = null, ?int $id_centro_costo = null): array
	{
		try {
			// Consulta base para obtener lista de balances con información del gasto fijo y centro de costo
			$query = <<<SQL
            SELECT
            	gfb.*,
            	gf.descripcion AS gasto_fijo_descripcion,
            	cc.nombre AS centro_costo_nombre
            FROM gastos_fijos_balance gfb
            LEFT JOIN gastos_fijos gf ON gfb.id_gasto_fijo = gf.id
            LEFT JOIN centros_costos cc ON gfb.id_centro_costo = cc.id
            WHERE 1=1
            SQL;

			// Agregar filtros opcionales
			if ($mes !== null) {
				$query .= " AND gfb.mes = :mes";
			}

			if ($id_gasto_fijo !== null) {
				$query .= " AND gfb.id_gasto_fijo = :id_gasto_fijo";
			}

			if ($anio !== null) {
				$query .= " AND gfb.anio = :anio";
			}

			if ($id_centro_costo !== null) {
				$query .= " AND gfb.id_centro_costo = :id_centro_costo";
			}

			$query .= " ORDER BY gfb.anio, gfb.mes, gf.descripcion";

			$statement = $conexion->prepare($query);

			// Bind de parámetros opcionales
			if ($mes !== null) {
				$statement->bindValue(':mes', $mes, PDO::PARAM_INT);
			}

			if ($id_gasto_fijo !== null) {
				$statement->bindValue(':id_gasto_fijo', $id_gasto_fijo, PDO::PARAM_INT);
			}

			if ($anio !== null) {
				$statement->bindValue(':anio', $anio, PDO::PARAM_INT);
			}

			if ($id_centro_costo !== null) {
				$statement->bindValue(':id_centro_costo', $id_centro_costo, PDO::PARAM_INT);
			}

			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener lista de GastosFijosBalance: " . $e->getMessage());
		}
	}

	/**
	 * Valida que exista el gasto fijo en la base de datos.
	 *
	 * @param int $id_gasto_fijo ID del gasto fijo a validar.
	 * @param PDO $conexion      Conexión PDO.
	 *
	 * @return bool True si existe y está activo, False en caso contrario.
	 * @throws Exception Si hay error en DB.
	 */
	private static function validarGastoFijoExiste(int $id_gasto_fijo, PDO $conexion): bool
	{
		try {
			$query = <<<SQL
            SELECT COUNT(*) as count
            FROM gastos_fijos
            WHERE id = :id_gasto_fijo AND estado = 1
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_gasto_fijo', $id_gasto_fijo, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return (int)$resultado['count'] > 0;

		} catch (PDOException $e) {
			throw new Exception("Error al validar existencia de gasto fijo: " . $e->getMessage());
		}
	}

	/**
	 * Valida que un valor sea positivo (mayor que cero).
	 *
	 * @param float|null $valor Valor a validar.
	 *
	 * @return bool True si el valor es válido, False en caso contrario.
	 */
	private function validarValorPositivo(?float $valor): bool
	{
		return $valor !== null && is_numeric($valor) && $valor > 0;
	}

	/**
	 * Valida que un mes esté en el rango válido (1-12).
	 *
	 * @param int|null $mes Mes a validar.
	 *
	 * @return bool True si el mes es válido, False en caso contrario.
	 */
	private function validarMes(?int $mes): bool
	{
		return $mes !== null && is_int($mes) && $mes >= 1 && $mes <= 12;
	}

	/**
	 * Valida que un año sea válido.
	 *
	 * @param int|null $anio Año a validar.
	 * @return bool True si el año es válido, False en caso contrario.
	 */
	private function validarAnio(?int $anio): bool
	{
		return $anio !== null && is_int($anio) && $anio >= 1900 && $anio <= 9999; // Ajusta el rango según necesidad
	}

	/**
	 * Valida que no exista un balance duplicado para el mismo gasto fijo, centro de costo, mes y año.
	 *
	 * @param int $id_gasto_fijo ID del gasto fijo.
	 * @param int $id_centro_costo ID del centro de costo.
	 * @param int $mes           Mes del balance.
	 * @param int $anio          Año del balance.
	 * @param PDO $conexion      Conexión PDO.
	 * @param int|null $excluir_id ID a excluir de la validación (para modificaciones).
	 *
	 * @return bool True si no existe duplicado, False si ya existe.
	 * @throws Exception Si hay error en DB.
	 */
	private static function validarBalanceUnico(int $id_gasto_fijo, int $id_centro_costo, int $mes, int $anio, PDO $conexion, ?int $excluir_id = null): bool
	{
		try {
			$query = <<<SQL
            SELECT COUNT(*) as count
            FROM gastos_fijos_balance
            WHERE id_gasto_fijo = :id_gasto_fijo AND id_centro_costo = :id_centro_costo AND mes = :mes AND anio = :anio
            SQL;

			if ($excluir_id !== null) {
				$query .= " AND id != :excluir_id";
			}

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_gasto_fijo', $id_gasto_fijo, PDO::PARAM_INT);
			$statement->bindValue(':id_centro_costo', $id_centro_costo, PDO::PARAM_INT);
			$statement->bindValue(':mes', $mes, PDO::PARAM_INT);
			$statement->bindValue(':anio', $anio, PDO::PARAM_INT);

			if ($excluir_id !== null) {
				$statement->bindValue(':excluir_id', $excluir_id, PDO::PARAM_INT);
			}

			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return (int)$resultado['count'] === 0;

		} catch (PDOException $e) {
			throw new Exception("Error al validar unicidad de balance: " . $e->getMessage());
		}
	}

	/**
	 * Crea un nuevo balance de gasto fijo en la base de datos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return int|false El ID del nuevo balance creado o false en caso de error.
	 * @throws Exception Si los datos requeridos están vacíos o hay error en DB.
	 */
	function crear(PDO $conexion): int|false
	{
		// Validaciones básicas sobre el objeto
		if ($this->getId_gasto_fijo() === null || $this->getId_gasto_fijo() <= 0) {
			throw new Exception("ID del gasto fijo es requerido para crear un balance.");
		}

		if ($this->getId_centro_costo() === null || $this->getId_centro_costo() <= 0) {
			throw new Exception("ID del centro de costo es requerido para crear un balance.");
		}

		if (!$this->validarValorPositivo($this->getValor())) {
			throw new Exception("El valor debe ser un número positivo mayor que cero.");
		}

		if (!$this->validarMes($this->getMes())) {
			throw new Exception("El mes debe ser un número entre 1 y 12.");
		}

		if (!$this->validarAnio($this->getAnio())) {
			throw new Exception("El año debe ser un número válido (ej. 2023).");
		}

		// Validar que el gasto fijo exista
		if (!self::validarGastoFijoExiste($this->getId_gasto_fijo(), $conexion)) {
			throw new Exception("El gasto fijo especificado no existe o está inactivo.");
		}

		// Validar que no exista un balance duplicado
		if (!self::validarBalanceUnico($this->getId_gasto_fijo(), $this->getId_centro_costo(), $this->getMes(), $this->getAnio(), $conexion)) {
			throw new Exception("Ya existe un balance para este gasto fijo, centro de costo, mes y año especificados.");
		}

		try {
			return $this->_insert($conexion);

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al crear balance de gasto fijo: " . $e->getMessage());
		} catch (Exception $e) {
			throw new Exception("Error al crear balance de gasto fijo: " . $e->getMessage());
		}
	}

	/**
	 * Modifica un balance de gasto fijo existente.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la modificación fue exitosa, False en caso contrario.
	 * @throws Exception Si los datos son inválidos o si ocurre un error de base de datos.
	 */
	function modificar(PDO $conexion): bool
	{
		if ($this->getId() <= 0) {
			throw new Exception("ID de balance de gasto fijo inválido para modificar.");
		}

		if ($this->getId_gasto_fijo() === null || $this->getId_gasto_fijo() <= 0) {
			throw new Exception("ID del gasto fijo es requerido.");
		}

		if ($this->getId_centro_costo() === null || $this->getId_centro_costo() <= 0) {
			throw new Exception("ID del centro de costo es requerido.");
		}

		if (!$this->validarValorPositivo($this->getValor())) {
			throw new Exception("El valor debe ser un número positivo mayor que cero.");
		}

		if (!$this->validarMes($this->getMes())) {
			throw new Exception("El mes debe ser un número entre 1 y 12.");
		}

		if (!$this->validarAnio($this->getAnio())) {
			throw new Exception("El año debe ser un número válido (ej. 2023).");
		}

		// Validar que el gasto fijo exista
		if (!self::validarGastoFijoExiste($this->getId_gasto_fijo(), $conexion)) {
			throw new Exception("El gasto fijo especificado no existe o está inactivo.");
		}

		// Validar que no exista un balance duplicado (excluyendo el actual)
		if (!self::validarBalanceUnico($this->getId_gasto_fijo(), $this->getId_centro_costo(), $this->getMes(), $this->getAnio(), $conexion, $this->getId())) {
			throw new Exception("Ya existe un balance para este gasto fijo, centro de costo, mes y año especificados.");
		}

		try {
			return $this->_update($conexion);

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al modificar balance de gasto fijo (ID: {$this->getId()}): " . $e->getMessage());
		}
	}

	/**
	 * Elimina un balance de gasto fijo.
	 *
	 * @param int $id       ID del balance de gasto fijo a eliminar.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la eliminación fue exitosa, False en caso contrario.
	 * @throws Exception Si ocurre un error de base de datos.
	 */
	public static function eliminar(int $id, PDO $conexion): bool
	{
		try {
			// Consulta para eliminar el balance
			$query = <<<SQL
            DELETE FROM gastos_fijos_balance
            WHERE id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);

			return $statement->execute();

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al eliminar balance de gasto fijo (ID: $id): " . $e->getMessage());
		}
	}

	/**
	 * Método privado para insertar un nuevo balance en la base de datos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return int|false El ID del nuevo balance creado o false en caso de error.
	 * @throws Exception Si hay error en la inserción.
	 */
	private function _insert(PDO $conexion): int|false
	{
		try {
			// Preparar la consulta INSERT usando Heredoc
			$query = <<<SQL
            INSERT INTO gastos_fijos_balance (
            	 id_gasto_fijo
            	,id_centro_costo
            	,anio
            	,valor
            	,mes
            ) VALUES (
            	 :id_gasto_fijo
            	,:id_centro_costo
            	,:anio
            	,:valor
            	,:mes
            )
            SQL;

			$statement = $conexion->prepare($query);

			// Bind de parámetros desde el objeto
			$statement->bindValue(':id_gasto_fijo', $this->getId_gasto_fijo(), PDO::PARAM_INT);
			$statement->bindValue(':id_centro_costo', $this->getId_centro_costo(), PDO::PARAM_INT);
			$statement->bindValue(':anio', $this->getAnio(), PDO::PARAM_INT);
			$statement->bindValue(':valor', $this->getValor(), PDO::PARAM_STR);
			$statement->bindValue(':mes', $this->getMes(), PDO::PARAM_INT);

			// Ejecutar la consulta
			$success = $statement->execute();

			if ($success) {
				// Devolver el ID del balance recién creado
				return (int)$conexion->lastInsertId();
			} else {
				return false; // Error en la ejecución
			}

		} catch (PDOException $e) {
			throw new Exception("Error en la inserción del balance de gasto fijo: " . $e->getMessage());
		}
	}

	/**
	 * Método privado para actualizar un balance existente en la base de datos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la actualización fue exitosa, False en caso contrario.
	 * @throws Exception Si hay error en la actualización.
	 */
	private function _update(PDO $conexion): bool
	{
		try {
			// Consulta para actualizar el balance
			$query = <<<SQL
            UPDATE gastos_fijos_balance SET
                id_gasto_fijo = :id_gasto_fijo,
                id_centro_costo = :id_centro_costo,
                anio = :anio,
                valor = :valor,
                mes = :mes
            WHERE
                id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_gasto_fijo', $this->getId_gasto_fijo(), PDO::PARAM_INT);
			$statement->bindValue(':id_centro_costo', $this->getId_centro_costo(), PDO::PARAM_INT);
			$statement->bindValue(':anio', $this->getAnio(), PDO::PARAM_INT);
			$statement->bindValue(':valor', $this->getValor(), PDO::PARAM_STR);
			$statement->bindValue(':mes', $this->getMes(), PDO::PARAM_INT);
			$statement->bindValue(':id', $this->getId(), PDO::PARAM_INT);

			// execute() devuelve true en éxito, false en error.
			return $statement->execute();

		} catch (PDOException $e) {
			throw new Exception("Error en la actualización del balance de gasto fijo: " . $e->getMessage());
		}
	}

	/**
	 * Obtiene balances por mes específico.
	 *
	 * @param int $mes       Mes a consultar (1-12).
	 * @param int $anio      Año a consultar.
	 * @param PDO $conexion  Conexión PDO.
	 * @param int|null $id_centro_costo Filtro opcional por centro de costo.
	 *
	 * @return array Array de objetos GastoFijoBalance.
	 * @throws Exception Si hay error en DB.
	 */
	public static function obtenerPorMesAnio(int $mes, int $anio, PDO $conexion, ?int $id_centro_costo = null): array
	{
		if ($mes < 1 || $mes > 12) {
			throw new Exception("El mes debe estar entre 1 y 12.");
		}
		// Aquí podrías añadir validación para $anio si es necesario, ej: if ($anio < 1900 || $anio > 9999) throw new Exception("Año inválido.");
		return self::obtenerTodos($conexion, $mes, null, $anio, $id_centro_costo);
	}

	/**
	 * Obtiene balances por gasto fijo específico.
	 *
	 * @param int $id_gasto_fijo ID del gasto fijo.
	 * @param PDO $conexion      Conexión PDO.
	 * @param int|null $anio     Filtro opcional por año.
	 * @param int|null $id_centro_costo Filtro opcional por centro de costo.
	 *
	 * @return array Array de objetos GastoFijoBalance.
	 * @throws Exception Si hay error en DB.
	 */
	public static function obtenerPorGastoFijo(int $id_gasto_fijo, PDO $conexion, ?int $anio = null, ?int $id_centro_costo = null): array
	{
		return self::obtenerTodos($conexion, null, $id_gasto_fijo, $anio, $id_centro_costo);
	}

	/**
	 * Calcula el total de balances para un mes específico.
	 *
	 * @param int $mes       Mes a consultar (1-12).
	 * @param int $anio      Año a consultar.
	 * @param PDO $conexion  Conexión PDO.
	 * @param int|null $id_centro_costo Filtro opcional por centro de costo.
	 *
	 * @return float Total de balances para el mes.
	 * @throws Exception Si hay error en DB.
	 */
	public static function calcularTotalPorMesAnio(int $mes, int $anio, PDO $conexion, ?int $id_centro_costo = null): float
	{
		if ($mes < 1 || $mes > 12) {
			throw new Exception("El mes debe estar entre 1 y 12.");
		}
		if ($anio < 1900 || $anio > 9999) { // Ajusta el rango según necesidad
			throw new Exception("El año debe ser un número válido (ej. 2023).");
		}

		try {
			$query = <<<SQL
            SELECT COALESCE(SUM(valor), 0) as total
            FROM gastos_fijos_balance
            WHERE mes = :mes AND anio = :anio
            SQL;

			if ($id_centro_costo !== null) {
				$query .= " AND id_centro_costo = :id_centro_costo";
			}

			$statement = $conexion->prepare($query);
			$statement->bindValue(':mes', $mes, PDO::PARAM_INT);
			$statement->bindValue(':anio', $anio, PDO::PARAM_INT);

			if ($id_centro_costo !== null) {
				$statement->bindValue(':id_centro_costo', $id_centro_costo, PDO::PARAM_INT);
			}

			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return (float)($resultado['total'] ?? 0);

		} catch (PDOException $e) {
			throw new Exception("Error al calcular total por mes: " . $e->getMessage());
		}
	}

	// --- Getters y Setters ---

	public function getId(): ?int
	{
		return $this->id;
	}

	public function setId(?int $id): self
	{
		$this->id = $id;
		return $this;
	}

	public function getId_gasto_fijo(): ?int
	{
		return $this->id_gasto_fijo;
	}

	public function setId_gasto_fijo(?int $id_gasto_fijo): self
	{
		if ($id_gasto_fijo !== null && $id_gasto_fijo <= 0) {
			throw new Exception("El ID del gasto fijo debe ser un número positivo.");
		}
		$this->id_gasto_fijo = $id_gasto_fijo;
		return $this;
	}

	public function getId_centro_costo(): ?int
	{
		return $this->id_centro_costo;
	}

	public function setId_centro_costo(?int $id_centro_costo): self
	{
		if ($id_centro_costo !== null && $id_centro_costo <= 0) {
			throw new Exception("El ID del centro de costo debe ser un número entero positivo.");
		}
		$this->id_centro_costo = $id_centro_costo;
		return $this;
	}

	public function getValor(): ?float
	{
		return $this->valor;
	}

	public function setValor(?float $valor): self
	{
		if ($valor !== null && !$this->validarValorPositivo($valor)) {
			throw new Exception("El valor debe ser un número positivo mayor que cero.");
		}
		$this->valor = $valor;
		return $this;
	}

	public function getMes(): ?int
	{
		return $this->mes;
	}

	public function setMes(?int $mes): self
	{
		if ($mes !== null && !$this->validarMes($mes)) {
			throw new Exception("El mes debe ser un número entre 1 y 12.");
		}
		$this->mes = $mes;
		return $this;
	}

	public function getAnio(): ?int
	{
		return $this->anio;
	}

	public function setAnio(?int $anio): self
	{
		if ($anio !== null && !$this->validarAnio($anio)) {
			throw new Exception("El año debe ser un número válido (ej. 2023).");
		}
		$this->anio = $anio;
		return $this;
	}


	public function getGasto_fijo_descripcion(): ?string
	{
		return $this->gasto_fijo_descripcion;
	}

	public function setGasto_fijo_descripcion(?string $gasto_fijo_descripcion): self
	{
		$this->gasto_fijo_descripcion = $gasto_fijo_descripcion;
		return $this;
	}

	public function getCentro_costo_nombre(): ?string
	{
		return $this->centro_costo_nombre;
	}

	public function setCentro_costo_nombre(?string $centro_costo_nombre): self
	{
		$this->centro_costo_nombre = $centro_costo_nombre;
		return $this;
	}

	// --- Métodos adicionales ---

	/**
	 * Obtiene el valor formateado en pesos colombianos.
	 * @return string
	 */
	public function getValorFormateado(): string
	{
		if ($this->valor === null) {
			return '$0';
		}
		return '$' . number_format($this->valor, 0, ',', '.');
	}

	/**
	 * Obtiene el nombre del mes en español.
	 * @return string
	 */
	public function getNombreMes(): string
	{
		$meses = [
			1 => 'Enero', 2 => 'Febrero', 3 => 'Marzo', 4 => 'Abril',
			5 => 'Mayo', 6 => 'Junio', 7 => 'Julio', 8 => 'Agosto',
			9 => 'Septiembre', 10 => 'Octubre', 11 => 'Noviembre', 12 => 'Diciembre'
		];

		return $meses[$this->mes] ?? 'Mes inválido';
	}

	/**
	 * Verifica si el balance es válido (tiene todos los datos requeridos).
	 * @return bool
	 */
	public function esValido(): bool
	{
		return $this->id_gasto_fijo !== null &&
		       $this->id_gasto_fijo > 0 &&
		       $this->id_centro_costo !== null &&
		       $this->id_centro_costo > 0 &&
		       $this->validarValorPositivo($this->valor) &&
		       $this->validarMes($this->mes) &&
		       $this->validarAnio($this->anio);
	}

	/**
	 * Convierte el objeto a array para facilitar la serialización.
	 * @return array
	 */
	public function toArray(): array
	{
		return [
			'id' => $this->id,
			'id_gasto_fijo' => $this->id_gasto_fijo,
			'id_centro_costo' => $this->id_centro_costo,
			'valor' => $this->valor,
			'mes' => $this->mes,
			'anio' => $this->anio,
			'gasto_fijo_descripcion' => $this->gasto_fijo_descripcion,
			'centro_costo_nombre' => $this->centro_costo_nombre,
			'valor_formateado' => $this->getValorFormateado(),
			'nombre_mes' => $this->getNombreMes()
		];
	}

}
