<?php
#region DOCS

/** @var array $centros_costos Lista de centros de costo activos */
/** @var array $metodos_pagos Lista de métodos de pago activos */
/** @var string|null $success_text Success message to display */
/** @var string|null $success_display Whether to show success message ('show' or null) */
/** @var string|null $error_text Error message to display */
/** @var string|null $error_display Whether to show error message ('show' or null) */

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Crear Nueva Venta</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>

	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/general/head.view.php'; ?>
	<?php #endregion HEAD ?>

	<!-- Toast Notifications CSS -->
	<link href="resources/css/toast-notifications.css" rel="stylesheet" />

	<style>
		/* Fix autocomplete dropdown positioning and visibility */
		#producto-results, #cliente-results {
			position: absolute !important;
			top: 100% !important;
			left: 0 !important;
			right: 0 !important;
			width: 100% !important;
			z-index: 1050 !important;
			border: 1px solid #dee2e6 !important;
			border-radius: 0.375rem !important;
			box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
			background-color: #fff !important;
			margin-top: 0.25rem;
		}

		#producto-results .dropdown-item, #cliente-results .dropdown-item {
			padding: 0.5rem 1rem !important;
			border-bottom: 1px solid #f8f9fa !important;
			color: #212529 !important;
			text-decoration: none !important;
		}

		#producto-results .dropdown-item:hover, #cliente-results .dropdown-item:hover {
			background-color: #e9ecef !important;
			color: #16181b !important;
		}

		/* Ensure parent container has relative positioning */
		.position-relative {
			position: relative !important;
		}
	</style>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/general/header.view.php'; ?>

	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/general/sidebar.view.php'; ?>

	<!-- BEGIN #content -->
	<div id="content" class="app-content">

            <?php #region PAGE HEADER ?>
            <div class="d-flex align-items-center mb-3">
                <div>
                    <h4 class="mb-0">Crear Nueva Venta</h4>
                    <p class="mb-0 text-muted">Registro de venta con gestión automática de inventario</p>
                </div>
                <div class="ms-auto">
                    <a href="ventas" class="btn btn-secondary">
                        <i class="fa fa-arrow-left fa-fw me-1"></i> Volver a Consulta
                    </a>
                </div>
            </div>

            <hr>
            <?php #endregion PAGE HEADER ?>

            <!-- BEGIN venta-form -->
            <form id="venta-form" class="needs-validation" novalidate>
                <!-- BEGIN basic-info-panel -->
                <div class="panel panel-inverse">
                    <div class="panel-heading">
                        <h4 class="panel-title">Información Básica</h4>
                    </div>
                    <div class="panel-body">
                        <div class="row g-3">
                            <!-- Cliente Selection -->
                            <div class="col-md-6">
                                <label for="cliente_search" class="form-label">Cliente</label>
                                <div class="position-relative">
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="cliente_search"
                                            placeholder="Buscar cliente por nombre o celular..." autocomplete="off">
                                        <button type="button" class="btn btn-outline-secondary" id="btn-nuevo-cliente">
                                            <i class="fa fa-plus"></i> Nuevo
                                        </button>
                                    </div>
                                    <div id="cliente-results" class="dropdown-menu w-100" style="max-height: 200px; overflow-y: auto;"></div>
                                </div>
                                <input type="hidden" id="id_cliente" name="id_cliente">
                                <small class="text-muted">
                                    <i class="fa fa-info-circle me-1"></i>Opcional - Deje vacío para venta sin cliente registrado
                                </small>
                            </div>

                            <!-- Centro de Costo -->
                            <div class="col-md-3">
                                <label for="id_centro_costo" class="form-label">Centro de Costo <span class="text-danger">*</span></label>
                                <select class="form-select" id="id_centro_costo" name="id_centro_costo" required>
                                    <option value="">Seleccione...</option>
                                    <?php foreach ($centros_costos as $centro): ?>
                                        <option value="<?php echo $centro->getId(); ?>">
                                            <?php echo htmlspecialchars($centro->getNombre()); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="invalid-feedback">
                                    Por favor seleccione un centro de costo.
                                </div>
                            </div>

                            <!-- Método de Pago -->
                            <div class="col-md-3">
                                <label for="id_metodo_pago" class="form-label">Método de Pago <span class="text-danger">*</span></label>
                                <select class="form-select" id="id_metodo_pago" name="id_metodo_pago" required>
                                    <option value="">Seleccione...</option>
                                    <?php foreach ($metodos_pagos as $metodo): ?>
                                        <option value="<?php echo $metodo->getId(); ?>">
                                            <?php echo htmlspecialchars($metodo->getDescripcion()); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="invalid-feedback">
                                    Por favor seleccione un método de pago.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- END basic-info-panel -->

                <!-- BEGIN productos-panel -->
                <div class="panel panel-inverse">
                    <div class="panel-heading">
                        <h4 class="panel-title">Productos de la Venta</h4>
                    </div>
                    <div class="panel-body">
                        <!-- Add Product Section -->
                        <div class="mb-4" id="add-product-section" style="display: none;">
                            <!-- Buscar Producto - Full Width Row -->
                            <div class="row g-3 mb-3">
                                <div class="col-12">
                                    <label for="producto_search" class="form-label">Buscar Producto</label>
                                    <div class="position-relative">
                                        <input type="text" class="form-control" id="producto_search"
                                            placeholder="Buscar producto por descripción..." autocomplete="off">
                                        <div id="producto-results" class="dropdown-menu w-100" style="max-height: 200px; overflow-y: auto;"></div>
                                    </div>
                                </div>
                            </div>

                            <!-- Quantity, Stock, and Unit Value Row -->
                            <div class="row g-3 mb-3">
                                <div class="col-md-4">
                                    <label for="cantidad_producto" class="form-label">Cantidad</label>
                                    <input type="number" class="form-control" id="cantidad_producto" min="1" value="1">
                                </div>
                                <div class="col-md-4">
                                    <label for="stock_disponible" class="form-label">Stock Disponible</label>
                                    <input type="text" class="form-control" id="stock_disponible" readonly placeholder="0">
                                </div>
                                <div class="col-md-4">
                                    <label for="valor_unitario" class="form-label">Valor Unitario</label>
                                    <input type="text" class="form-control" id="valor_unitario" readonly>
                                </div>
                            </div>

                            <!-- Stock Validation Message -->
                            <div id="stock-validation-message" class="row g-3 mb-2" style="display: none;">
                                <div class="col-12">
                                    <small class="text-muted">
                                        <i class="fa fa-info-circle me-1"></i>La cantidad solicitada excede el stock disponible
                                    </small>
                                </div>
                            </div>

                            <!-- Add Product Button - Full Width Row -->
                            <div class="row g-3">
                                <div class="col-12">
                                    <button type="button" class="btn btn-primary w-100" id="btn-agregar-producto" disabled>
                                        <i class="fa fa-plus me-2"></i>Agregar Producto a la Venta
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Products Table -->
                        <div class="table-responsive">
                            <table class="table table-hover table-sm" id="productos-table">
                                <thead class="table-dark">
                                    <tr>
                                        <th style="width: 60px;" class="align-middle">Acciones</th>
                                        <th class="align-middle">Producto</th>
                                        <th style="width: 100px;" class="align-middle text-center">Cantidad</th>
                                        <th style="width: 120px;" class="align-middle text-end">Valor Unit.</th>
                                        <th style="width: 120px;" class="align-middle text-end">Valor Total</th>
                                    </tr>
                                </thead>
                                <tbody id="productos-table-body">
                                    <!-- Products will be added dynamically -->
                                </tbody>
                                <tfoot>
                                    <tr class="table-info">
                                        <th colspan="4" class="text-end align-middle">Total Venta:</th>
                                        <th id="total-venta" class="align-middle text-end">$0</th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>

                        <!-- Empty state -->
                        <div id="empty-products" class="text-center p-4">
                            <i class="fa fa-shopping-cart fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No hay productos agregados</h5>
                            <p class="text-muted">Seleccione un centro de costo para comenzar a agregar productos</p>
                        </div>
                    </div>
                </div>
                <!-- END productos-panel -->

                <!-- BEGIN actions-panel -->
                <div class="panel panel-inverse">
                    <div class="panel-body">
                        <div class="d-flex justify-content-between">
                            <a href="ventas" class="btn btn-secondary">
                                <i class="fa fa-arrow-left"></i> Volver a Consulta
                            </a>
                            <div>
                                <button type="button" class="btn btn-outline-secondary me-2" id="btn-limpiar">
                                    <i class="fa fa-refresh"></i> Limpiar
                                </button>
                                <button type="submit" class="btn btn-success" id="btn-guardar" disabled>
                                    <i class="fa fa-save"></i> Guardar Venta
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- END actions-panel -->
            </form>
            <!-- END venta-form -->
    </div>
    <!-- END #content -->
</div>
<!-- END #app -->

<!-- Toast Container -->
<div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 1055;">
    <div id="toast-notification" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <i id="toast-icon" class="fa fa-check-circle text-success me-2"></i>
            <strong id="toast-title" class="me-auto">Notificación</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body" id="toast-message">
            Mensaje de notificación
        </div>
    </div>
</div>

<!-- BEGIN nuevo-cliente-modal -->
<div class="modal fade" id="nuevo-cliente-modal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Crear Nuevo Cliente</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="nuevo-cliente-form">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="nuevo_cliente_nombre" class="form-label">Nombre <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="nuevo_cliente_nombre" required>
                    </div>
                    <div class="mb-3">
                        <label for="nuevo_cliente_celular" class="form-label">Celular</label>
                        <input type="text" class="form-control" id="nuevo_cliente_celular">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-primary">Crear Cliente</button>
                </div>
            </form>
        </div>
    </div>
</div>
<!-- END nuevo-cliente-modal -->

<!-- ================== BEGIN core-js ================== -->
<?php require_once __ROOT__ . '/views/general/core_js.view.php'; ?>
<!-- ================== END core-js ================== -->

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize the sales creation interface
    initSalesCreation();
});

function initSalesCreation() {
    // Elements
    const ventaForm          = document.getElementById('venta-form');
    const centroCostoSelect  = document.getElementById('id_centro_costo');
    const addProductSection  = document.getElementById('add-product-section');
    const emptyProducts      = document.getElementById('empty-products');
    const productosTableBody = document.getElementById('productos-table-body');
    const btnGuardar         = document.getElementById('btn-guardar');
    const btnLimpiar         = document.getElementById('btn-limpiar');

    // Cliente search elements
    const clienteSearch     = document.getElementById('cliente_search');
    const clienteResults    = document.getElementById('cliente-results');
    const idClienteInput    = document.getElementById('id_cliente');
    const btnNuevoCliente   = document.getElementById('btn-nuevo-cliente');
    const nuevoClienteModal = new bootstrap.Modal(document.getElementById('nuevo-cliente-modal'));
    const nuevoClienteForm  = document.getElementById('nuevo-cliente-form');

    // Product search elements
    const productoSearch     = document.getElementById('producto_search');
    const productoResults    = document.getElementById('producto-results');
    const cantidadProducto   = document.getElementById('cantidad_producto');
    const stockDisponible    = document.getElementById('stock_disponible');
    const valorUnitario      = document.getElementById('valor_unitario');
    const btnAgregarProducto = document.getElementById('btn-agregar-producto');

    // State
    let selectedProduct = null;
    let ventaDetalles = [];
    let searchTimeout = null;

    // Initialize
    updateUI();

    // Event Listeners
    centroCostoSelect.addEventListener('change', function() {
        if (this.value) {
            addProductSection.style.display = 'block';
            emptyProducts.style.display = 'none';
            // Clear products when centro costo changes and there are products
            if (ventaDetalles.length > 0) {
                if (confirm('Cambiar el centro de costo eliminará todos los productos agregados. ¿Continuar?')) {
                    clearProducts();
                } else {
                    // Revert selection
                    this.value = ventaDetalles.length > 0 ? ventaDetalles[0].id_centro_costo : '';
                    return;
                }
            }
        } else {
            addProductSection.style.display = 'none';
            if (ventaDetalles.length === 0) {
                emptyProducts.style.display = 'block';
            }
        }
        updateUI();
    });

    // Cliente search functionality
    clienteSearch.addEventListener('input', function() {
        const termino = this.value.trim();

        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            if (termino.length >= 2) {
                searchClientes(termino);
            } else {
                clienteResults.style.display = 'none';
            }
        }, 300);
    });

    clienteSearch.addEventListener('blur', function() {
        // Delay hiding results to allow clicking
        setTimeout(() => {
            clienteResults.style.display = 'none';
        }, 200);
    });

    // Nuevo cliente functionality
    btnNuevoCliente.addEventListener('click', function() {
        nuevoClienteModal.show();
    });

    nuevoClienteForm.addEventListener('submit', function(e) {
        e.preventDefault();
        createCliente();
    });

    // Product search functionality
    productoSearch.addEventListener('input', function() {
        const termino = this.value.trim();

        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            if (termino.length >= 2) {
                searchProductos(termino);
            } else {
                productoResults.style.display = 'none';
            }
        }, 300);
    });

    productoSearch.addEventListener('blur', function() {
        setTimeout(() => {
            productoResults.style.display = 'none';
        }, 200);
    });

    cantidadProducto.addEventListener('input', updateAddButton);

    btnAgregarProducto.addEventListener('click', addProductToVenta);

    // Form submission
    ventaForm.addEventListener('submit', function(e) {
        e.preventDefault();
        if (this.checkValidity() && ventaDetalles.length > 0) {
            saveVenta();
        } else {
            this.classList.add('was-validated');
            if (ventaDetalles.length === 0) {
                showError('Debe agregar al menos un producto a la venta.');
            }
        }
    });

    btnLimpiar.addEventListener('click', function() {
        if (confirm('¿Está seguro de que desea limpiar todos los datos?')) {
            clearForm();
        }
    });

    // Functions
    function searchClientes(termino) {
        const formData = new FormData();
        formData.append('action', 'search_clientes');
        formData.append('termino', termino);

        fetch('crear-venta', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayClienteResults(data.clientes);
            }
        })
        .catch(error => console.error('Error:', error));
    }

    function displayClienteResults(clientes) {
        clienteResults.innerHTML = '';

        if (clientes.length > 0) {
            clientes.forEach(cliente => {
                const item = document.createElement('a');
                item.className = 'dropdown-item';
                item.href = '#';
                item.textContent = cliente.texto;
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    selectCliente(cliente);
                });
                clienteResults.appendChild(item);
            });
            clienteResults.style.display = 'block';
        } else {
            clienteResults.style.display = 'none';
        }
    }

    function selectCliente(cliente) {
        clienteSearch.value = cliente.texto;
        idClienteInput.value = cliente.id;
        clienteResults.style.display = 'none';
    }

    function createCliente() {
        const formData = new FormData();
        formData.append('action', 'create_cliente');
        formData.append('nombre', document.getElementById('nuevo_cliente_nombre').value);
        formData.append('celular', document.getElementById('nuevo_cliente_celular').value);

        fetch('crear-venta', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                selectCliente(data.cliente);
                nuevoClienteModal.hide();
                nuevoClienteForm.reset();
                showSuccess(data.message);
            } else {
                showError(data.message);
            }
        })
        .catch(error => {
            showError('Error de conexión al crear cliente');
            console.error('Error:', error);
        });
    }

    function searchProductos(termino) {
        const formData = new FormData();
        formData.append('action', 'search_productos');
        formData.append('termino', termino);

        fetch('crear-venta', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayProductoResults(data.productos);
            }
        })
        .catch(error => console.error('Error:', error));
    }

    function displayProductoResults(productos) {
        productoResults.innerHTML = '';

        // Filter out already added products
        const availableProducts = productos.filter(p =>
            !ventaDetalles.some(d => d.id_producto === p.id)
        );

        if (availableProducts.length > 0) {
            availableProducts.forEach(producto => {
                const item = document.createElement('a');
                item.className = 'dropdown-item';
                item.href = '#';
                item.innerHTML = `${producto.descripcion} - ${producto.valor_formateado}`;
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    selectProducto(producto);
                });
                productoResults.appendChild(item);
            });
            productoResults.style.display = 'block';
        } else {
            productoResults.style.display = 'none';
        }
    }

    function selectProducto(producto) {
        selectedProduct = producto;
        productoSearch.value = producto.descripcion;
        valorUnitario.value = producto.valor_formateado;
        productoResults.style.display = 'none';

        // Check stock
        checkStock(producto.id);
    }

    function checkStock(idProducto) {
        const idCentroCosto = centroCostoSelect.value;

        if (!idCentroCosto) {
            stockDisponible.value = '0';
            updateAddButton();
            return;
        }

        const formData = new FormData();
        formData.append('action', 'check_stock');
        formData.append('id_centro_costo', idCentroCosto);
        formData.append('id_producto', idProducto);

        fetch('crear-venta', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                stockDisponible.value = data.stock;
                stockDisponible.className = data.tiene_stock ? 'form-control' : 'form-control text-danger';
                updateAddButton();
            }
        })
        .catch(error => console.error('Error:', error));
    }

    function updateAddButton() {
        const cantidad   = parseInt(cantidadProducto.value) || 0;
        const stock      = parseInt(stockDisponible.value) || 0;
        const hasProduct = selectedProduct !== null;
        const stockValidationMessage = document.getElementById('stock-validation-message');

        // Check if button should be disabled
        const isDisabled = !hasProduct || cantidad <= 0 || cantidad > stock || stock <= 0;
        btnAgregarProducto.disabled = isDisabled;

        // Show stock validation message only when:
        // 1. Product is selected
        // 2. Quantity is greater than 0
        // 3. Quantity exceeds available stock
        // 4. Stock is greater than 0 (to avoid showing message when no stock data)
        const showStockMessage = hasProduct && cantidad > 0 && cantidad > stock && stock >= 0;
        stockValidationMessage.style.display = showStockMessage ? 'block' : 'none';
    }

    function addProductToVenta() {
        if (!selectedProduct) return;

        const cantidad = parseInt(cantidadProducto.value);
        const valorTotal = selectedProduct.valor * cantidad;

        const detalle = {
            id_producto           : selectedProduct.id,
            descripcion           : selectedProduct.descripcion,
            cantidad              : cantidad,
            valor                 : selectedProduct.valor,
            valor_formateado      : selectedProduct.valor_formateado,
            valor_total           : valorTotal,
            valor_total_formateado: formatCurrency(valorTotal)
        };

        ventaDetalles.push(detalle);
        updateProductsTable();
        clearProductForm();
        updateUI();
    }

    function updateProductsTable() {
        productosTableBody.innerHTML = '';

        ventaDetalles.forEach((detalle, index) => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td class="text-center align-middle">
                    <button type="button" class="btn btn-danger btn-xs" onclick="removeProduct(${index})" title="Eliminar producto">
                        <i class="fa fa-trash"></i>
                    </button>
                </td>
                <td class="align-middle">${detalle.descripcion}</td>
                <td class="text-center align-middle">
                    <input type="number" class="form-control form-control-sm" value="${detalle.cantidad}"
                           min="1" onchange="updateQuantity(${index}, this.value)">
                </td>
                <td class="text-end align-middle">${detalle.valor_formateado}</td>
                <td class="text-end align-middle">${detalle.valor_total_formateado}</td>
            `;
            productosTableBody.appendChild(row);
        });

        updateTotal();
    }

    function updateTotal() {
        const total = ventaDetalles.reduce((sum, detalle) => sum + detalle.valor_total, 0);
        document.getElementById('total-venta').textContent = formatCurrency(total);
    }

    function clearProductForm() {
        selectedProduct        = null;
        productoSearch.value   = '';
        cantidadProducto.value = '1';
        stockDisponible.value  = '';
        valorUnitario.value    = '';
        document.getElementById('stock-validation-message').style.display = 'none';
        updateAddButton();
    }

    function clearProducts() {
        ventaDetalles = [];
        updateProductsTable();
        updateUI();
    }

    function clearForm() {
        ventaForm.reset();
        ventaForm.classList.remove('was-validated');
        clienteSearch.value  = '';
        idClienteInput.value = '';
        clearProducts();
        clearProductForm();
        addProductSection.style.display = 'none';
        emptyProducts.style.display = 'block';
        updateUI();
    }

    function updateUI() {
        const hasProducts = ventaDetalles.length > 0;
        const hasCentroCosto = centroCostoSelect.value !== '';

        btnGuardar.disabled = !hasProducts;

        if (hasProducts) {
            emptyProducts.style.display = 'none';
            // Disable centro costo if products exist
            centroCostoSelect.disabled = true;
        } else {
            centroCostoSelect.disabled = false;
            if (!hasCentroCosto) {
                emptyProducts.style.display = 'block';
            }
        }
    }

    function saveVenta() {
        const formData = new FormData();
        formData.append('action', 'create_venta');
        formData.append('id_cliente', idClienteInput.value);
        formData.append('id_centro_costo', centroCostoSelect.value);
        formData.append('id_metodo_pago', document.getElementById('id_metodo_pago').value);
        formData.append('detalles', JSON.stringify(ventaDetalles));

        btnGuardar.disabled = true;
        btnGuardar.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Guardando...';

        fetch('crear-venta', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSweetAlertSuccess('¡Éxito!', data.message);
                setTimeout(() => {
                    window.location.href = 'ventas';
                }, 2000);
            } else {
                showError(data.message);
                btnGuardar.disabled = false;
                btnGuardar.innerHTML = '<i class="fa fa-save"></i> Guardar Venta';
            }
        })
        .catch(error => {
            showError('Error de conexión al guardar venta');
            btnGuardar.disabled = false;
            btnGuardar.innerHTML = '<i class="fa fa-save"></i> Guardar Venta';
            console.error('Error:', error);
        });
    }

    // Global functions for table actions
    window.removeProduct = function(index) {
        const producto = ventaDetalles[index];

        swal({
            title: "¿Estás seguro?",
            text: `¿Deseas eliminar "${producto.descripcion}" de la venta?`,
            icon: "warning",
            buttons: {
                cancel: {
                    text: "Cancelar",
                    value: null,
                    visible: true,
                    className: "btn-secondary",
                    closeModal: true,
                },
                confirm: {
                    text: "Sí, eliminar",
                    value: true,
                    visible: true,
                    className: "btn-danger",
                    closeModal: true
                }
            },
            dangerMode: true,
        })
        .then((willDelete) => {
            if (willDelete) {
                ventaDetalles.splice(index, 1);
                updateProductsTable();
                updateUI();
                showToastNotification('success', 'Producto eliminado', `"${producto.descripcion}" ha sido eliminado de la venta.`);
            }
        });
    };

    window.updateQuantity = function(index, newQuantity) {
        const cantidad = parseInt(newQuantity) || 1;
        ventaDetalles[index].cantidad = cantidad;
        ventaDetalles[index].valor_total = ventaDetalles[index].valor * cantidad;
        ventaDetalles[index].valor_total_formateado = formatCurrency(ventaDetalles[index].valor_total);
        updateProductsTable();
    };

    function formatCurrency(amount) {
        return '$' + new Intl.NumberFormat('es-CO').format(amount);
    }

    function showToastNotification(type, title, message) {
        const toast = document.getElementById('toast-notification');
        const toastIcon = document.getElementById('toast-icon');
        const toastTitle = document.getElementById('toast-title');
        const toastMessage = document.getElementById('toast-message');

        // Configure toast based on type
        if (type === 'success') {
            toastIcon.className = 'fa fa-check-circle text-success me-2';
            toast.className = 'toast border-success';
        } else if (type === 'error') {
            toastIcon.className = 'fa fa-exclamation-circle text-danger me-2';
            toast.className = 'toast border-danger';
        } else if (type === 'warning') {
            toastIcon.className = 'fa fa-exclamation-triangle text-warning me-2';
            toast.className = 'toast border-warning';
        } else {
            toastIcon.className = 'fa fa-info-circle text-info me-2';
            toast.className = 'toast border-info';
        }

        toastTitle.textContent = title;
        toastMessage.textContent = message;

        // Show toast
        const bsToast = new bootstrap.Toast(toast, {
            autohide: true,
            delay: 4000
        });
        bsToast.show();
    }

    function showError(message) {
        showToastNotification('error', 'Error', message);
    }

    function showSuccess(message) {
        showToastNotification('success', 'Éxito', message);
    }
}
</script>
</body>
</html>
